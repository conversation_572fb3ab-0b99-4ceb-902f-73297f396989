"use client";

import { globalStore } from "@/store/global";
import { useProfileStore } from "@/store/profile";
import { useCookies } from "next-client-cookies";
import { useRouter } from "next/navigation";
import { useEffect, useCallback, useState, useRef } from "react";
import { API_ENDPOINTS, AUTH_ROUTES, LOADING_MESSAGES, COOKIE_NAMES } from "@/constants";

// Add session caching to avoid repeated API calls
let sessionCache: { user: any; timestamp: number } | null = null;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes cache

const useGetUser = () => {
  const { user, setUser } = useProfileStore(state => state);
  const { setLoading } = globalStore(state => state);
  const [isLoading, setIsLoading] = useState(true);
  const hasInitializedRef = useRef(false); // Use ref instead of state to avoid re-renders

  const router = useRouter();
  const cookies = useCookies();

  const noData = useCallback((shouldRedirect = true) => {
    setUser(null);
    setLoading(false);
    setIsLoading(false);
    sessionCache = null; // Clear cache on auth failure
    
    // Don't auto-redirect on upload page - let the page handle it
    if (shouldRedirect && typeof window !== 'undefined' && !window.location.pathname.includes('/upload')) {
      router.push(AUTH_ROUTES.SIGN_IN);
    }
  }, [setUser, setLoading, router]);

  const fetchUser = useCallback(async (forceRefresh = false) => {
    if (hasInitializedRef.current && !forceRefresh) {
      return;
    }

    if (!hasInitializedRef.current) {
      hasInitializedRef.current = true;
    }

    // Quick cookie check first
    const accessToken = cookies.get(COOKIE_NAMES.SESSION);
    if (!accessToken) {
      noData(false);
      return;
    }

    // Skip cache if force refresh
    if (!forceRefresh && sessionCache && (Date.now() - sessionCache.timestamp) < CACHE_DURATION) {
      setUser(sessionCache.user);
      setLoading(false);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setLoading(LOADING_MESSAGES.LOADING_USER);

    try {
      // Use direct fetch with shorter timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      const response = await fetch(API_ENDPOINTS.AUTH.SESSION, {
        headers: {
          "Authorization": `Bearer ${accessToken}`,
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const result = await response.json();
        if (result.data) {
          const apiUser = result.data;
          const supabaseUser = {
            id: apiUser.id,
            email: apiUser.email,
            created_at: apiUser.created_at,
            updated_at: apiUser.updated_at,
            app_metadata: {},
            user_metadata: {},
            aud: 'authenticated',
            confirmation_sent_at: null,
            confirmed_at: null,
            email_confirmed_at: null,
            identities: [],
            last_sign_in_at: null,
            phone: null,
            phone_confirmed_at: null,
            recovery_sent_at: null,
            role: 'authenticated',
          };
          
          // Cache the successful result
          sessionCache = {
            user: supabaseUser,
            timestamp: Date.now()
          };
          
          setUser(supabaseUser as any);
          setLoading(false);
          setIsLoading(false);
        } else {
          noData(false);
        }
      } else {
        noData(false);
      }
    } catch (error) {
      // If it's an abort error, still try to use cached data
      if (error instanceof Error && error.name === 'AbortError') {
        console.warn('Session check timed out, using cached data if available');
        if (sessionCache && (Date.now() - sessionCache.timestamp) < CACHE_DURATION * 2) {
          setUser(sessionCache.user);
          setLoading(false);
          setIsLoading(false);
          return;
        }
      }
      noData(false);
    }
  }, [cookies, noData, setLoading, setIsLoading, setUser]); // Removed hasInitialized dependency

  useEffect(() => {
    fetchUser();
  }, [fetchUser]); // Include fetchUser dependency

  // Add method to clear cache when needed
  const clearCache = useCallback(() => {
    sessionCache = null;
    hasInitializedRef.current = false; // Allow re-initialization
  }, []);

  const refreshUser = useCallback(async () => {
    sessionCache = null; // Clear cache

    const refreshToken = cookies.get(COOKIE_NAMES.REFRESH);
    if (refreshToken) {
      try {
        const response = await fetch(API_ENDPOINTS.AUTH.REFRESH, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ refresh_token: refreshToken }),
        });

        if (response.ok) {
          const result = await response.json();
          if (result.success && result.data?.session) {
            await fetchUser(true);
            return;
          }
        }
      } catch (error) {
        console.error('Failed to refresh session:', error);
      }
    }

    await fetchUser(true);
  }, [fetchUser, cookies]);

  return { user, setUser, loading: isLoading, clearCache, refreshUser };
};

export default useGetUser;
