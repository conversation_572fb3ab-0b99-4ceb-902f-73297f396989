import { render } from '@react-email/render'
import CommonTemplate from './CommonTemplate'
import { Link, Section, Text } from '@react-email/components'
import { IGame } from '@/store/singleGameStore'
import moment from 'moment'

const sendOTP = (data: any) => {
  const previewText = `Verify your email address`
  const body = (
    <>
      <Section className="mx-auto w-[90%] text-start ">
        <Text className="text-[16px] ">
          To verify your email address, please use the One-Time Password (OTP)
          below:
        </Text>
      </Section>
      <div className="mx-auto text-start">
        <div className="p-5 text-center text-3xl font-semibold text-[#00ff85]">
          {data.otp}
        </div>
      </div>
      <Section className="mx-auto w-[90%] text-start ">
        <Text className="text-[16px] ">
          This OTP is valid for a limited time only. If you did not make this
          request, please ignore this email.
          <br />
          <br />
          For questions or further assistance, contact us at{' '}
          <Link href="mailto:<EMAIL>" className="">
            <EMAIL>
          </Link>
        </Text>
      </Section>
    </>
  )
  return (
    <div>
      {render(
        <CommonTemplate
          name={data.name}
          previewText={previewText}
          body={body}
        />,
        {
          pretty: true,
        },
      )}
    </div>
  )
}

const renewGameSubscription = (dapp: IGame, profile: any) => {
  const validTill = moment(dapp.validTill).diff(moment(), 'days')
  const previewText =
    dapp.validTill ?
      `Subscription for ${dapp.title} will expire in ${validTill} days`
    : `Subscription Expired for ${dapp.title}`
  const body = (
    <>
      <Section className="mx-auto w-[90%] text-start ">
        <Text className="text-[16px] ">{previewText}</Text>
        <Text className="text-[16px] ">
          Click the button below to renew your subscription for {dapp.title}
        </Text>
      </Section>
      <Section className="mx-auto w-[90%] text-start ">
        <Text className="text-[16px] ">
          <div className="mx-auto text-center">
            <a
              href={`${process.env.HOST}/home/<USER>
              target="_blank"
              className="text-center text-xl font-semibold text-[#00ff85]"
            >
              Renew Subscription
            </a>
          </div>
          <br />
          For questions or further assistance, contact us at{' '}
          <Link href="mailto:<EMAIL>" className="">
            <EMAIL>
          </Link>
        </Text>
      </Section>
    </>
  )
  return (
    <div>
      {render(
        <CommonTemplate
          name={profile?.displayName || 'User'}
          previewText={previewText}
          body={body}
        />,
        {
          pretty: true,
        },
      )}
    </div>
  )
}

const creditCardRecepit = (data: any) => {
  const body = (
    <>
      <Section className="mx-auto w-[90%] text-start ">
        <Text className="text-[16px] ">
          We have successfully processed your payment for the order number
          <br />
          {data?.orderNumber}
        </Text>
      </Section>
      <Section className="mx-auto w-[90%] text-start ">
        <Text className="text-[16px] ">
          Transaction Amount: {data?.amount} USD
          <br /> Payment Method: Credit card
          <br /> Thank you for your payment!
        </Text>
      </Section>
      <Section className="mx-auto w-[90%] text-start ">
        <Text className="text-[16px] ">
          Should you have any questions or require further assistance, please
          reach out to us at{' '}
          <Link href="mailto:<EMAIL>" className="">
            <EMAIL>
          </Link>
          .
          <br />
          <br />
          If you did not authorize this payment, kindly ignore this email.
        </Text>
      </Section>
    </>
  )
  return body
}

const cryptoReceipt = (data: any) => {
  const href = `${process.env.NEXT_PUBLIC_EXPLORER}tx/${data.hash}`
  const tranHash = `${data.hash.slice(0, 6)}...${data.hash.slice(data.hash.length - 4)}`
  const body = (
    <>
      <Section className="mx-auto w-[90%] text-start ">
        <Text className="text-[16px] ">
          We have received your crypto payment
        </Text>
      </Section>
      <Section className="mx-auto w-[90%] text-start ">
        <Text className="text-[16px] ">
          Transaction Amount: {data?.amount} $BNRY
          <br /> Payment Method: Crypto
          <br /> Transaction #:{' '}
          <Link target="_blank" href={href}>
            {' '}
            {tranHash}{' '}
          </Link>
          <br /> Thank you for your payment!
        </Text>
      </Section>
      <Section className="mx-auto w-[90%] text-start ">
        <Text className="text-[16px] ">
          Should you have any questions or require further assistance, please
          reach out to us at{' '}
          <Link href="mailto:<EMAIL>" className="">
            <EMAIL>
          </Link>
          .
          <br />
          <br />
          If you did not authorize this payment, kindly ignore this email.
        </Text>
      </Section>
    </>
  )
  return body
}

const sendPaymentReceipt = (data: any) => {
  const previewText = `Payment receipt`
  const body =
    data?.paymentType === 'cc' ? creditCardRecepit(data) : cryptoReceipt(data)
  return (
    <div>
      {render(
        <CommonTemplate
          name={data.name}
          previewText={previewText}
          body={body}
        />,
        {
          pretty: true,
        },
      )}
    </div>
  )
}

const EmailBody = {
  sendOTP,
  renewGameSubscription,
  sendPaymentReceipt,
}

export default EmailBody
