// utils/fetcher.ts
import { LOADER } from "@/constants/loader";
import { globalStore } from "@/store/global";

type Method = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

type FetchOptions = {
    method?: Method;
    body?: any;
    headers?: Record<string, string>;
    auth?: boolean;
    parse?: boolean;
};

export const fetcher = async <T>(
    url: string,
    {
        method = 'GET',
        body,
        headers = {},
        auth = true,
        parse = true,
    }: FetchOptions = {}
): Promise<T> => {
    const setLoading = globalStore.getState().setLoading;

    const defaultHeaders: Record<string, string> = {
        'Content-Type': 'application/json',
        ...headers,
    };

    if (auth) {
        const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null;
        if (token) {
            defaultHeaders['Authorization'] = `Bearer ${token}`;
        }
    }

    try {
        const pop = url.split('/').pop();
        const msg = pop ? LOADER[pop] : 'Loading';
        setLoading(msg);
        const response = await fetch(`/api/v1/${url}`, {
            method,
            headers: defaultHeaders,
            body: body ? JSON.stringify(body) : undefined,
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(errorText || 'API Error');
        }

        return parse ? await response.json() : (response as unknown as T);
    } catch (error: any) {
        console.error(`[fetcher error] ${error.message}`);
        throw error;
    } finally {
        setLoading(false);
    }
};
