import { AuthError, AuthUser } from "./types";
import Cookies from "js-cookie";

/**
 * Formats authentication errors into user-friendly messages
 */
export function formatAuthError(error: any): AuthError {
  if (typeof error === "string") {
    return { message: error };
  }

  if (error?.message) {
    // Common Supabase auth error mappings
    const errorMappings: Record<string, string> = {
      "Invalid login credentials": "The email or password you entered is incorrect. Please try again.",
      "Email not confirmed": "Please check your email and click the confirmation link before signing in.",
      "User not found": "No account found with this email address.",
      "Signup requires a valid password": "Please provide a valid password.",
      "User already registered": "An account with this email already exists. Try signing in instead.",
      "Password should be at least 6 characters": "Password must be at least 6 characters long.",
      "Unable to validate email address": "Please enter a valid email address.",
      "Too many requests": "Too many attempts. Please wait a moment before trying again.",
      "Invalid email or password": "The email or password you entered is incorrect.",
      "Email link is invalid or has expired": "This link has expired. Please request a new password reset.",
      "Token has expired or is invalid": "This session has expired. Please sign in again.",
    };

    const mappedMessage = errorMappings[error.message] || error.message;
    
    return {
      message: mappedMessage,
      status: error.status,
      code: error.code,
    };
  }

  return {
    message: "An unexpected error occurred. Please try again.",
  };
}

/**
 * Validates password strength
 */
export function validatePasswordStrength(password: string): {
  score: number;
  feedback: string[];
  isValid: boolean;
} {
  const feedback: string[] = [];
  let score = 0;

  if (password.length >= 8) {
    score += 1;
  } else {
    feedback.push("Use at least 8 characters");
  }

  if (password.length >= 12) {
    score += 1;
  }

  if (/[A-Z]/.test(password)) {
    score += 1;
  } else {
    feedback.push("Include uppercase letters");
  }

  if (/[a-z]/.test(password)) {
    score += 1;
  } else {
    feedback.push("Include lowercase letters");
  }

  if (/[0-9]/.test(password)) {
    score += 1;
  } else {
    feedback.push("Include numbers");
  }

  if (/[^A-Za-z0-9]/.test(password)) {
    score += 1;
  } else {
    feedback.push("Include special characters");
  }

  // Check for common patterns
  if (!/(.)\1{2,}/.test(password)) {
    score += 1;
  } else {
    feedback.push("Avoid repeated characters");
  }

  return {
    score: Math.min(score, 5),
    feedback,
    isValid: score >= 4,
  };
}

/**
 * Generates a secure session token
 */
export function generateSessionToken(): string {
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
}

/**
 * Safely stores authentication tokens
 */
export function storeAuthTokens(accessToken: string, refreshToken: string, expiresAt?: number) {
  const cookieOptions = {
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict' as const,
    path: '/',
  };

  // Store access token with shorter expiry
  Cookies.set('session', accessToken, {
    ...cookieOptions,
    expires: 1, // 1 day
  });

  // Store refresh token with longer expiry
  Cookies.set('refresh', refreshToken, {
    ...cookieOptions,
    expires: 30, // 30 days
  });

  if (expiresAt) {
    Cookies.set('session_expires', expiresAt.toString(), {
      ...cookieOptions,
      expires: 1,
    });
  }
}

/**
 * Clears authentication tokens
 */
export function clearAuthTokens() {
  Cookies.remove('session', { path: '/' });
  Cookies.remove('refresh', { path: '/' });
  Cookies.remove('session_expires', { path: '/' });
}

/**
 * Gets stored authentication tokens
 */
export function getStoredTokens() {
  return {
    accessToken: Cookies.get('session'),
    refreshToken: Cookies.get('refresh'),
    expiresAt: Cookies.get('session_expires'),
  };
}

/**
 * Checks if a session is expired
 */
export function isSessionExpired(expiresAt?: string | number): boolean {
  if (!expiresAt) return false;
  
  const expiry = typeof expiresAt === 'string' ? parseInt(expiresAt) : expiresAt;
  return Date.now() >= expiry * 1000;
}

/**
 * Formats user display name
 */
export function formatUserDisplayName(user: AuthUser): string {
  if (user.user_metadata?.full_name) {
    return user.user_metadata.full_name;
  }
  
  const firstName = user.user_metadata?.firstName || user.user_metadata?.first_name;
  const lastName = user.user_metadata?.lastName || user.user_metadata?.last_name;
  
  if (firstName && lastName) {
    return `${firstName} ${lastName}`;
  }
  
  if (firstName) {
    return firstName;
  }
  
  return user.email?.split('@')[0] || 'User';
}

/**
 * Gets user avatar URL with fallback
 */
export function getUserAvatarUrl(user: AuthUser): string {
  if (user.user_metadata?.avatar_url) {
    return user.user_metadata.avatar_url;
  }

  // Return local default avatar placeholder
  return '/default-avatar-photo-placeholder-profile-icon-vector.jpg';
}

/**
 * Rate limiting utility
 */
export class RateLimiter {
  private attempts: Map<string, { count: number; resetTime: number }> = new Map();
  
  constructor(
    private maxAttempts: number = 5,
    private windowMs: number = 15 * 60 * 1000 // 15 minutes
  ) {}
  
  isAllowed(key: string): boolean {
    const now = Date.now();
    const attempt = this.attempts.get(key);
    
    if (!attempt || now > attempt.resetTime) {
      this.attempts.set(key, { count: 1, resetTime: now + this.windowMs });
      return true;
    }
    
    if (attempt.count >= this.maxAttempts) {
      return false;
    }
    
    attempt.count++;
    return true;
  }
  
  reset(key: string): void {
    this.attempts.delete(key);
  }
  
  getRemainingTime(key: string): number {
    const attempt = this.attempts.get(key);
    if (!attempt) return 0;
    
    return Math.max(0, attempt.resetTime - Date.now());
  }
}

/**
 * Device fingerprinting for security
 */
export function getDeviceFingerprint(): string {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  ctx?.fillText('Device fingerprint', 10, 10);
  
  const components = [
    navigator.userAgent,
    navigator.language,
    screen.width + 'x' + screen.height,
    new Date().getTimezoneOffset(),
    canvas.toDataURL(),
  ];
  
  return btoa(components.join('|')).slice(0, 32);
}

/**
 * Validates email format more strictly
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
  return emailRegex.test(email);
}

/**
 * Sanitizes user input
 */
export function sanitizeInput(input: string): string {
  return input.trim().replace(/[<>]/g, '');
}

/**
 * Debounce utility for form validation
 */
export function debounce<T extends (...args: any[]) => void>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}
