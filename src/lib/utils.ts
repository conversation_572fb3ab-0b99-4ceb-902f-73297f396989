import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}


export const openDapp = (dapp: any) => {
  const dappData = {
    type: 'dapp-click',
    dappId: dapp.id,
    dappName: dapp.title,
    dappDesc: dapp.description,
    thumbnail: `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/${dapp?.thumbnail}`,
    url: dapp.link,
    category: dapp.category,
    banner: `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/${dapp?.banner}`,
  }
  window.parent.postMessage(dappData, process.env.NEXT_PUBLIC_ONE_DIGITAL!);
}
