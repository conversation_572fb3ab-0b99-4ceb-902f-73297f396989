import { createBrowserClient } from "@supabase/ssr";
import { SUPABASE_ENV, SUPABASE_CONFIG, validateSupabaseEnv } from "@/constants/supabase";

// Validate required environment variables
validateSupabaseEnv();

export const createClient = () => createBrowserClient(
  SUPABASE_ENV.URL!,
  SUPABASE_ENV.ANON_KEY!,
  SUPABASE_CONFIG.CLIENT
);

const supabaseClient = createClient();
export { supabaseClient };
