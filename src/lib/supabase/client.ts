import { createBrowserClient } from "@supabase/ssr";
import { SUPABASE_ENV, SUPABASE_CONFIG, validateSupabaseEnv } from "@/constants/supabase";

// Validate required environment variables
validateSupabaseEnv();

export const createClient = () => createBrowserClient(
  SUPABASE_ENV.URL!,
  SUPABASE_ENV.ANON_KEY!,
  SUPABASE_CONFIG.CLIENT
);

const supabaseClient = createClient();

/**
 * Creates an authenticated Supabase client using session tokens from cookies
 * This is needed for operations that require authentication like storage uploads
 */
export const createAuthenticatedClient = async () => {
  const client = createClient();

  // Get session token from cookies
  const sessionToken = document.cookie
    .split('; ')
    .find(row => row.startsWith('session='))
    ?.split('=')[1];

  const refreshToken = document.cookie
    .split('; ')
    .find(row => row.startsWith('refresh='))
    ?.split('=')[1];

  if (!sessionToken) {
    throw new Error('No authentication session found. Please sign in again.');
  }

  try {
    const { error } = await client.auth.setSession({
      access_token: sessionToken,
      refresh_token: refreshToken || ''
    });

    if (error) {
      throw new Error(`Authentication failed: ${error.message}`);
    }

    // Verify the session was set correctly
    const { data: { user }, error: userError } = await client.auth.getUser();

    if (userError || !user) {
      throw new Error('Failed to authenticate user session');
    }

    console.log('✅ Authenticated client created for user:', user.email);
    return client;
  } catch (error) {
    console.error('🚨 Authentication error:', error);
    throw error;
  }
};

export { supabaseClient };
