import { supabaseClient } from '@/lib/supabase/client'

const tableName = `${process?.env?.NEXT_PUBLIC_DB_PREFIX || ''}Profile`

export const getUsersById = async (id: string) => {
  try {
    const { data: user, error } = await supabaseClient
      .from('auth.users')
      .select('id, email')
      .eq('id', id)
      .single()

    if (user && !error) {
      return { user, error: null }
    }
  } catch (e) {
    console.warn('auth.users table not accessible:', e)
  }
  
  try {
    const { data: user, error } = await supabaseClient
      .from('users')
      .select('*')
      .eq('id', id)
      .single()

    if (user && !error) {
      return { user, error: null }
    }
  } catch (e) {
    console.warn('users table not accessible:', e)
  }

  console.warn('Could not fetch user, proceeding without email')
  return { user: null, error: null }
}
