import Stripe from 'stripe'
import { supabaseClient } from '@/lib/supabase/client'
import { serviceRoleSupabase } from '@/lib/supabase/service-role'
import EmailBody from '@/email-templates/EmailBodyHandler'
import moment from 'moment'
import { doEmail } from '@/app/api/v1/emails'

const gTable = `${process?.env?.NEXT_PUBLIC_DB_PREFIX || ''}Games`

export const updateDappSubscription = async (
  gameId: string,
  validTill: Date,
  paymentId?: string | null,
  hash?: string | null,
) => {
  const { data: gData, error: gError } = await getGameById(gameId)
  if (gData?.validTill && moment(gData.validTill).isAfter(moment())) {
    validTill = moment(gData.validTill).add(Number(process?.env?.GAME_VALIDATION), 'days').toDate()
  }
  // Use service role client for webhook operations to bypass RLS
  const { data, error } = await serviceRoleSupabase
    .from(gTable)
    .update({
      paymentId,
      validTill,
      hash,
    })
    .eq('id', gameId)
    .select('*')
    .single()

  if (error) {
    console.error('Error updating subscription:', error)
    throw error
  }
  return { data, error }
}

export const addNewDapp = async (
  session: Stripe.Checkout.Session,
  validTill: Date,
) => {
  // Check if user exists first
  const userId = session.metadata?.userId;
  if (userId) {
    console.log('🔍 Checking if user exists:', userId);
    const { data: userExists } = await supabaseClient
      .from('users')
      .select('id')
      .eq('id', userId)
      .single();
    
    if (!userExists) {
      console.warn('⚠️ User not found, will create dApp without user reference');
    }
  }

  const dappData = {
    name: session.metadata?.dappName,
    category: session.metadata?.category,
    description: session.metadata?.description,
    live_url: session.metadata?.liveUrl,
    logo: session.metadata?.logo,
    user_id: userId || null, // Set to null if user doesn't exist
    plan_type: session.metadata?.planType,
    paymentid: session.id,
    validtill: validTill, // lowercase
    hash: session.metadata?.hash || null, // lowercase
    // Note: created_at has default value in database, so we don't need to set it
  }
  
  console.log('🔍 Creating DApp with data:', dappData)
  
  // Use service role client for webhook operations to bypass RLS
  // This ensures DApp creation always succeeds after successful payment
  const { data, error } = await serviceRoleSupabase
    .from('dapps')
    .insert([dappData])
    .select('*')
    .single()
    
  if (error) {
    console.error('❌ DApp creation error details:', {
      error,
      dappData,
      hasServiceRole: !!process.env.SUPABASE_SERVICE_ROLE_KEY
    })
  } else {
    console.log('✅ DApp created successfully:', data)
  }
    
  return { data, error }
}

export const getGames = async (user: any, skip: string, limit: string) => {
  const { data, error } = await supabaseClient
    .from(gTable)
    .select('*')
    .limit(Number(limit) || 10)
    .order('validTill')
    .range(Number(skip) || 0, (Number(skip) || 0) + (Number(limit) || 10) - 1)
    .eq('createdBy', user.id)
  const items = data?.map((d) => {
    const expired = moment(d.validTill).diff(moment(), 'hours')
    const isActive = expired > 0
    return {
      ...d,
      isActive,
      playCount: isActive ? d.playCount : 'N/A',
      ratingCount: isActive ? d.ratingCount : 'N/A',
      totalTimePlayed: isActive ? d.totalTimePlayed : 'N/A',
      avgRating: isActive ? d.avgRating : 'N/A',
      avgTime: isActive ? d.avgTime : 'N/A',
    }
  })
  return { data: items, error }
}

export const getGameById = async (id: string) => {
  const { data, error } = await supabaseClient
    .from(gTable)
    .select('*')
    .eq('id', id)
    .single()
  return { data, error }
}

export const expiredGames = async (period: number) => {
  const { data, error } = await supabaseClient
    .from(gTable)
    .select('*')

  return data
}

export const getGamesCount = async (user: any) => {
  const { count } = await supabaseClient
    .from(gTable)
    .select('*', { count: 'exact', head: true })
    .eq('createdBy', user.id)
  return { count }
}

export const sendPaymentReceipt = async (session: Stripe.Checkout.Session) => {
  const emailBody = EmailBody.sendPaymentReceipt({
    name: session.customer_details!.name,
    orderNumber: session.payment_intent,
    amount: session.amount_total! / 100,
    paymentType: 'cc',
  }) // Add data here
  const newBody = await emailBody.props.children
  await doEmail(
    session.customer_details!.email!,
    `Payment Successful: ${session.payment_intent}`,
    newBody,
  )
}
