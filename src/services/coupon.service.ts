import { supabase } from '@/lib/supabase/constants'
import { serviceRoleSupabase } from '@/lib/supabase/service-role'

export interface Coupon {
  id: string
  coupon: string
  created_at: string
  is_applied: boolean
  user_id?: string
  dapp_id?: number
}

/**
 * Validate coupon - check if exists and not used
 */
export const validateCoupon = async (code: string): Promise<{ data: Coupon | null; error: any; isValid: boolean }> => {
  try {
    const { data, error } = await supabase
      .from('coupons')
      .select('*')
      .eq('coupon', code.toUpperCase())
      .eq('is_applied', false)
      .is('user_id', null)
      .is('dapp_id', null)
      .limit(1) 

    if (error) {
      return { data: null, error: error.message, isValid: false }
    }

    if (!data || data.length === 0) {
      return { data: null, error: 'Coupon not found or already used', isValid: false }
    }

    // Get first valid coupon
    const coupon = data[0]
    return { data: coupon, error: null, isValid: true }
    
  } catch (err: any) {
    return { data: null, error: err.message || 'Failed to validate coupon', isValid: false }
  }
}

/**
 * Mark coupon as used
 */
export const applyCoupon = async (couponId: string, userId: string, dappId?: string | number): Promise<{ data: Coupon | null; error: any }> => {
  try {
    // Use service role client to bypass RLS for coupon updates
    const { data, error } = await serviceRoleSupabase
      .from('coupons')
      .update({
        is_applied: true,
        user_id: userId,
        dapp_id: dappId,
      })
      .eq('id', couponId)
      .eq('is_applied', false)
      .select('*')
      .single()

    if (error) {
      return { data: null, error: error.message }
    }

    return { data, error: null }
  } catch (err: any) {
    return { data: null, error: err.message || 'Failed to use coupon' }
  }
}

/**
 * Get coupon usage stats (admin function)
 */
export const getCouponStats = async () => {
  try {
    const { data, error } = await supabase
      .from('coupons')
      .select('*')

    if (error) {
      return { data: null, error: error.message }
    }

    const stats = {
      total: data.length,
      used: data.filter((c: any) => c.is_applied).length,
      available: data.filter((c: any) => !c.is_applied).length
    }

    return { data: stats, error: null }
  } catch (err: any) {
    return { data: null, error: err.message }
  }
}