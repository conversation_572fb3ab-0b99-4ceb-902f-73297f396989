/**
 * API Constants
 * Centralized API endpoints and configurations
 */

// Base API configuration
export const API_CONFIG = {
  BASE_URL: '/api/v1',
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
} as const;

// API Endpoints
export const API_ENDPOINTS = {
  // Authentication endpoints
  AUTH: {
    SESSION: `${API_CONFIG.BASE_URL}/session`,
    SIGN_IN: `${API_CONFIG.BASE_URL}/session/sign-in`,
    SIGN_UP: `${API_CONFIG.BASE_URL}/session/sign-up`,
    SIGN_OUT: `${API_CONFIG.BASE_URL}/session/sign-out`,
    RESET_LINK: `${API_CONFIG.BASE_URL}/session/reset-link`,
    RESET_PASSWORD: `${API_CONFIG.BASE_URL}/session/reset-password`,
    CHANGE_PASSWORD: `${API_CONFIG.BASE_URL}/session/change-password`,
    OAUTH_CALLBACK: `${API_CONFIG.BASE_URL}/session/oauth-callback`,
  },
  
  // DApps endpoints
  DAPPS: {
    BASE: `${API_CONFIG.BASE_URL}/dapps`,
    LIST: `${API_CONFIG.BASE_URL}/dapps`,
    CREATE: `${API_CONFIG.BASE_URL}/dapps`,
    UPDATE: (id: string) => `${API_CONFIG.BASE_URL}/dapps/${id}`,
    DELETE: (id: string) => `${API_CONFIG.BASE_URL}/dapps/${id}`,
    GET_BY_ID: (id: string) => `${API_CONFIG.BASE_URL}/dapps/${id}`,
  },
  
  // Payment endpoints
  PAYMENT: {
    CHECKOUT_SESSION: `${API_CONFIG.BASE_URL}/checkout-session`,
  },

  // Stripe endpoints
  STRIPE: {
    WEBHOOK: `${API_CONFIG.BASE_URL}/stripe/webhook`,
  },
  
  // Pricing endpoints
  PRICING: {
    PLANS: `${API_CONFIG.BASE_URL}/pricing-plans`,
  },
  
  // Coupons endpoints
  COUPONS: {
    VALIDATE: `${API_CONFIG.BASE_URL}/coupons`,
  },
  
  // File upload endpoints
  UPLOAD: {
    FILE: `${API_CONFIG.BASE_URL}/upload`,
    IMAGE: `${API_CONFIG.BASE_URL}/upload/image`,
  },
} as const;

// HTTP Methods
export const HTTP_METHODS = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  DELETE: 'DELETE',
  PATCH: 'PATCH',
} as const;

// HTTP Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
} as const;

// Content Types
export const CONTENT_TYPES = {
  JSON: 'application/json',
  FORM_DATA: 'multipart/form-data',
  URL_ENCODED: 'application/x-www-form-urlencoded',
  TEXT: 'text/plain',
} as const;

// Request Headers
export const REQUEST_HEADERS = {
  CONTENT_TYPE: 'Content-Type',
  AUTHORIZATION: 'Authorization',
  ACCEPT: 'Accept',
  USER_AGENT: 'User-Agent',
  X_FORWARDED_FOR: 'x-forwarded-for',
  X_REAL_IP: 'x-real-ip',
} as const;

// Cookie Names
export const COOKIE_NAMES = {
  SESSION: 'session',
  REFRESH: 'refresh',
  THEME: 'theme',
} as const;

// Cookie Configuration
export const COOKIE_CONFIG = {
  SESSION: {
    PATH: '/',
    SAME_SITE: 'Strict' as const,
    HTTP_ONLY: true,
    SECURE: true,
    MAX_AGE: 24 * 60 * 60, // 24 hours
  },
  REFRESH: {
    PATH: '/',
    SAME_SITE: 'Strict' as const,
    HTTP_ONLY: true,
    SECURE: true,
    MAX_AGE: 7 * 24 * 60 * 60, // 7 days
  },
} as const;
