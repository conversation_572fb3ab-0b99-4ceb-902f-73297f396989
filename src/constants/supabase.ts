/**
 * Supabase Configuration Constants
 * Centralized Supabase environment variables and configuration
 */

// Environment Variables with validation
export const SUPABASE_ENV = {
  URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
  ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
  SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY,
} as const;

// Validation function for required environment variables
export function validateSupabaseEnv() {
  const missing: string[] = [];
  
  if (!SUPABASE_ENV.URL) {
    missing.push('NEXT_PUBLIC_SUPABASE_URL');
  }
  
  if (!SUPABASE_ENV.ANON_KEY) {
    missing.push('NEXT_PUBLIC_SUPABASE_ANON_KEY');
  }
  
  if (missing.length > 0) {
    throw new Error(`Missing required Supabase environment variables: ${missing.join(', ')}`);
  }
}

// Validate service role key separately since it's only needed for server-side operations
export function validateServiceRoleKey() {
  if (!SUPABASE_ENV.SERVICE_ROLE_KEY) {
    console.warn('SUPABASE_SERVICE_ROLE_KEY is not set. Some server-side operations may not work.');
    return false;
  }
  return true;
}

// Supabase Client Configuration
export const SUPABASE_CONFIG = {
  CLIENT: {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true
    }
  },
  SERVICE_ROLE: {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
} as const;

// Supabase API Endpoints
export const SUPABASE_ENDPOINTS = {
  AUTH: {
    TOKEN: `${SUPABASE_ENV.URL}/auth/v1/token`,
    SIGNUP: `${SUPABASE_ENV.URL}/auth/v1/signup`,
    USER: `${SUPABASE_ENV.URL}/auth/v1/user`,
    LOGOUT: `${SUPABASE_ENV.URL}/auth/v1/logout`,
    RECOVER: `${SUPABASE_ENV.URL}/auth/v1/recover`,
  },
  REST: {
    USERS: `${SUPABASE_ENV.URL}/rest/v1/users`,
    DAPPS: `${SUPABASE_ENV.URL}/rest/v1/dapps`,
  },
  STORAGE: {
    BASE: `${SUPABASE_ENV.URL}/storage/v1`,
    OBJECT: `${SUPABASE_ENV.URL}/storage/v1/object`,
    PUBLIC: `${SUPABASE_ENV.URL}/storage/v1/object/public`,
  }
} as const;

// Headers configuration
export const SUPABASE_HEADERS = {
  BASE: {
    'Content-Type': 'application/json',
    'apikey': SUPABASE_ENV.ANON_KEY || '',
  },
  AUTH: {
    'Content-Type': 'application/json',
    'apikey': SUPABASE_ENV.ANON_KEY || '',
  },
  SERVICE_ROLE: {
    'Content-Type': 'application/json',
    'apikey': SUPABASE_ENV.SERVICE_ROLE_KEY || '',
    'Authorization': `Bearer ${SUPABASE_ENV.SERVICE_ROLE_KEY || ''}`,
  }
} as const;

// Storage bucket names
export const SUPABASE_BUCKETS = {
  DAPP_LOGOS: 'dapp-logos',
  SCREENSHOTS: 'screenshots',
  USER_AVATARS: 'user-avatars',
} as const; 