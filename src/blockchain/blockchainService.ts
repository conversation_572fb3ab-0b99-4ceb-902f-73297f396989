import { ethers } from "ethers";
import { <PERSON><PERSON>, createPublicClient, getContract, http } from "viem";
import { optimism, optimismSepolia } from "viem/chains";
import { TOKEN_ABI } from "./tokenABI";
import {
  BNRY_ADDRESS,
  BNRY_RPC_URL,
  conversions,
  RECEIVER_ADDRESS,
  USDT_ADDRESS,
  USDT_DECIMALS,
} from "./blockchainConsts";
import {
  Blockchain_Tx_Data,
  ENV,
  Hex_Bytes,
  Transaction_Details,
} from "./blockchainTypes";

export default class blockchainService {
  env: ENV;
  bnryContract: ethers.Contract;
  usdtContract: ethers.Contract;
  bnryProvider : ethers.JsonRpcProvider;
  OPPublicClient;
  readbnryContract;
  readUSDTContract;

  constructor() {
    this.env = process.env.NEXT_PUBLIC_NETWORK as ENV;
    this.OPPublicClient = createPublicClient({
      chain: this.env === "mainnet" ? optimism : optimismSepolia,
      transport: http(),
    });
    this.readbnryContract = getContract({
      address: BNRY_ADDRESS[this.env] as Hex_Bytes,
      abi: TOKEN_ABI as Abi,
      client: this.OPPublicClient,
    });
    this.readUSDTContract = getContract({
      address: USDT_ADDRESS[this.env] as Hex_Bytes,
      abi: TOKEN_ABI as Abi,
      client: this.OPPublicClient,
    });
    this.bnryContract = new ethers.Contract(BNRY_ADDRESS[this.env], TOKEN_ABI);
    this.usdtContract = new ethers.Contract(USDT_ADDRESS[this.env], TOKEN_ABI);
    this.bnryProvider = new ethers.JsonRpcProvider(BNRY_RPC_URL[this.env]);
  }

  public sendBNRY(amount: number): Blockchain_Tx_Data {
    try {
      const data = this.bnryContract.interface.encodeFunctionData("transfer", [
        RECEIVER_ADDRESS[this.env],
        conversions.toWei(amount),
      ]) as Hex_Bytes;
      const to = BNRY_ADDRESS[this.env] as Hex_Bytes;
      return {
        data,
        to,
        value: ethers.parseEther("0.0"),
      };
    } catch (error) {
      throw error;
    }
  }

  public sendUSDT(amount: number): Blockchain_Tx_Data {
    try {
      const data = this.bnryContract.interface.encodeFunctionData("transfer", [
        RECEIVER_ADDRESS[this.env],
        conversions.toWei(amount, USDT_DECIMALS),
      ]) as Hex_Bytes;
      const to = USDT_ADDRESS[this.env] as Hex_Bytes;
      return {
        data,
        to,
        value: ethers.parseEther("0.0"),
      };
    } catch (error) {
      throw error;
    }
  }

  async getBNRYTxInfo(hash: string): Promise<Transaction_Details> {
    try {
      const tx = await this.OPPublicClient.getTransactionReceipt({
        hash: hash as Hex_Bytes,
      });
      if (tx.to?.toLowerCase() !== BNRY_ADDRESS[this.env].toLowerCase())
        throw new Error("Invalid Tokens Sent");
      const block = await this.OPPublicClient.getBlock({
        blockNumber: tx?.blockNumber,
      });
      const parsedLogs = tx.logs.map((log) =>
        this.bnryContract.interface.parseLog(log)
      );
      const transferLog = parsedLogs.filter(
        (log) => log?.name === "Transfer"
      )[0];
      const from = transferLog?.args[0];
      const to = transferLog?.args[1];
      const value = conversions.toEther(transferLog?.args[2]);
      return {
        from,
        to,
        value,
        correctReceiver:
          to.toLowerCase() === RECEIVER_ADDRESS[this.env].toLowerCase(),
        timestamp: Number(block.timestamp),
        date: new Date(Number(block?.timestamp) * 1000).toLocaleString(),
      };
    } catch (error) {
      throw error;
    }
  }

  async getUSDTTxInfo(hash: string): Promise<Transaction_Details> {
    try {
      const tx = await this.OPPublicClient.getTransactionReceipt({
        hash: hash as Hex_Bytes,
      });
      if (tx.to?.toLowerCase() !== USDT_ADDRESS[this.env].toLowerCase())
        throw new Error("Invalid Tokens Sent");
      const block = await this.OPPublicClient.getBlock({
        blockNumber: tx?.blockNumber,
      });
      const parsedLogs = tx.logs.map((log) =>
        this.usdtContract.interface.parseLog(log)
      );
      const transferLog = parsedLogs.filter(
        (log) => log?.name === "Transfer"
      )[0];
      const from = transferLog?.args[0];
      const to = transferLog?.args[1];
      const value = conversions.toEther(transferLog?.args[2], USDT_DECIMALS);
      return {
        from,
        to,
        value,
        correctReceiver:
          to.toLowerCase() === RECEIVER_ADDRESS[this.env].toLowerCase(),
        timestamp: Number(block.timestamp),
        date: new Date(Number(block?.timestamp) * 1000).toLocaleString(),
      };
    } catch (error) {
      throw error;
    }
  }

  async getBNRYBalance(address: string): Promise<string> {
    try {
      const balanceInwei = await this.readbnryContract.read.balanceOf([
        address,
      ]);
      return conversions.toEther(String(balanceInwei));
    } catch (error) {
      throw error;
    }
  }

  async getUSDTBalance(address: string): Promise<string> {
    try {
      const balanceInwei = await this.readUSDTContract.read.balanceOf([
        address,
      ]);
      return conversions.toEther(String(balanceInwei), USDT_DECIMALS);
    } catch (error) {
      throw error;
    }
  }

  async getNativeBNRYTxInfo(hash: string): Promise<Transaction_Details> {
    try {
      const tx = await this.bnryProvider.getTransactionReceipt(hash);
  
      if (!tx) throw new Error("Transaction not found");
  
      const transaction = await this.bnryProvider.getTransaction(hash);
  
      if (!transaction || !transaction.value || transaction.value === BigInt(0))
        throw new Error("No native currency transfer in this transaction");
  
      const block = await this.bnryProvider.getBlock(tx.blockNumber);
  
      return {
        from: transaction.from,
        to: transaction?.to as string,
        value: conversions.toEther(String(transaction.value)),
        correctReceiver:
          transaction.to?.toLowerCase() ===
          RECEIVER_ADDRESS[this.env].toLowerCase(),
        timestamp: Number(block?.timestamp),
        date: new Date(Number(block?.timestamp) * 1000).toLocaleString(),
      };
    } catch (error) {
      throw error;
    }
  }

  async getNativeBNRYBalance(address: string): Promise<string> {
    try {
      const balanceInWei = await this.bnryProvider.getBalance(address);
      return conversions.toEther(balanceInWei.toString());
    } catch (error) {
      throw error;
    }
  }

  

  async getBNRYPrice(): Promise<number> {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_TBH_CRON_API}token-price`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error(
          `Failed to fetch top addresses: ${response.statusText}`
        );
      }

      const data = await response.json();

      if (!data.price) {
        throw new Error("Invalid response format: missing price");
      }

      return parseFloat(data.price);
    } catch (error) {
      console.error("Error fetching token price :", error);
      throw error;
    }
  }
  
  
}
