import { NextRequest, NextResponse } from 'next/server';
import { supabaseClient } from '@/lib/supabase/client';
import { serverClient } from '@/lib/supabase/server';
import { cookies } from 'next/headers';

export async function GET(req: NextRequest) {
  try {
    const cookieStore = await cookies();
    const supabase = serverClient(cookieStore);
    const sessionToken = req.cookies.get("session")?.value;
    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    await supabase.auth.setSession({
      access_token: sessionToken,
      refresh_token: req.cookies.get("refresh")?.value || ''
    });

    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'User not found or session expired' }, 
        { status: 401 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '8');
    const userId = user.id; // Always use the authenticated user's ID

    // Validate pagination parameters
    if (page < 1 || limit < 1 || limit > 50) {
      return NextResponse.json(
        { error: 'Invalid pagination parameters' }, 
        { status: 400 }
      );
    }

    const from = (page - 1) * limit;
    const to = from + limit - 1;

    // Get total count
    const { count, error: countError } = await supabaseClient
      .from('dapps')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);

    if (countError) {
      return NextResponse.json(
        { error: 'Failed to fetch dapps count' }, 
        { status: 500 }
      );
    }

    // Get paginated data
    const { data: dapps, error: fetchError } = await supabaseClient
      .from('dapps')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range(from, to);

    if (fetchError) {
      return NextResponse.json(
        { error: fetchError }, 
        { status: 500 }
      );
    }

    const totalPages = Math.ceil((count || 0) / limit);



    return NextResponse.json({
      success: true,
      data: {
        data: dapps || [],
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    });

  } catch (error) {
    console.error('Error in dapps API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
