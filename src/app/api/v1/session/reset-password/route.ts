import { _Error, _Success } from "@/lib/response";
import { supabase } from "@/lib/supabase/constants";
import { NextRequest, NextResponse } from "next/server";

export const runtime = "nodejs";
export const dynamic = "force-dynamic";

export async function POST(req: NextRequest) {
  try {
    const { password, token } = await req.json();

    console.log('🔍 Reset Password API Debug:', {
      hasPassword: !!password,
      hasTokenInBody: !!token,
      tokenLength: token?.length,
      authHeader: req.headers.get("Authorization")?.substring(0, 20) + '...'
    });

    if (!password) {
      return _Error("Password is required");
    }

    const accessToken = token || req.headers.get("Authorization")?.replace('Bearer ', '');

    if (!accessToken) {
      console.log('❌ No access token found');
      return _Error("Reset token missing");
    }

    console.log('✅ Access token found, length:', accessToken.length);

    const tokenParts = accessToken.split('.');
    console.log('🔍 Token Structure:', {
      totalLength: accessToken.length,
      parts: tokenParts.length,
      partLengths: tokenParts.map((part: string) => part.length),
      firstPart: tokenParts[0]?.substring(0, 20) + '...',
      isValidJWT: tokenParts.length === 3,
      startsWithBearer: accessToken.startsWith('Bearer '),
    });

    // For password reset, use verifyOtp with recovery type since token is not a JWT
    const { data: userData, error: userError } = await supabase.auth.verifyOtp({
      token_hash: accessToken,
      type: 'recovery'
    });

    console.log('🔍 User Validation:', {
      hasUserData: !!userData?.user,
      userError: userError?.message,
      userId: userData?.user?.id,
      hasSession: !!userData?.session
    });

    if (userError || !userData?.user) {
      console.log('❌ User validation failed:', userError?.message);
      return _Error("Invalid or expired reset token");
    }
    
    // Use the session from verifyOtp response
    if (userData?.session) {
      await supabase.auth.setSession({
        access_token: userData.session.access_token,
        refresh_token: userData.session.refresh_token,
      });
    }
    
    // Update password
    console.log('🔄 Updating password...');
    const { error: updateError } = await supabase.auth.updateUser({
      password: password,
    });

    if (updateError) {
      console.log('❌ Password update failed:', updateError.message);
      return _Error(updateError.message || "Failed to update password");
    }

    console.log('✅ Password updated successfully');
    return _Success("Password updated successfully");
    
  } catch (error) {
    return _Error("An unexpected error occurred");
  }
}
