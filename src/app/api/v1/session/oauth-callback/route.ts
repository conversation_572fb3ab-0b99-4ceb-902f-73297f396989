import { _Error, _Success } from "@/lib/response";
import { createClient } from "@supabase/supabase-js";
import { NextRequest, NextResponse } from "next/server";

export const runtime = "nodejs";
export const dynamic = "force-dynamic";

// Use regular client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

// Helper function to get client IP
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const real = request.headers.get('x-real-ip');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  if (real) {
    return real.trim();
  }
  
  return 'unknown';
}

export async function POST(req: NextRequest) {
  try {
    // Security headers
    const response = NextResponse.json({});
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('X-XSS-Protection', '1; mode=block');

    // Get the access token from Authorization header
    const authHeader = req.headers.get('Authorization');
    const accessToken = authHeader?.replace('Bearer ', '');

    if (!accessToken) {
      return _Error("Authorization token required", 401);
    }

    // Verify the token and get user info
    const { data: { user }, error: userError } = await supabase.auth.getUser(accessToken);

    if (userError || !user) {
      return _Error("Invalid or expired token", 401);
    }

    // Get request body
    const body = await req.json();
    const { provider } = body;

    // Get client IP for security logging
    const clientIP = getClientIP(req);

    // Check if user record exists in our users table
    const { data: existingUser, error: checkError } = await supabase
      .from("users")
      .select("*")
      .eq("id", user.id)
      .single();

    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 is "not found"
      console.error("Error checking existing user:", checkError);
      return _Error("Database error", 500);
    }

    const now = new Date().toISOString();

    if (!existingUser) {
      // Create new user record for OAuth user
      const { error: insertError } = await supabase
        .from("users")
        .insert({
          id: user.id,
          email: user.email,
          created_at: now,
          updated_at: now,
          email_confirmed_at: user.email_confirmed_at || now, // OAuth users are pre-confirmed
          last_sign_in_at: now,
          last_sign_in_ip: clientIP,
          auth_provider: provider || 'oauth',
          // Store additional OAuth metadata
          provider_id: user.user_metadata?.provider_id,
          avatar_url: user.user_metadata?.avatar_url,
          full_name: user.user_metadata?.full_name || user.user_metadata?.name,
        });

      if (insertError) {
        console.error("Error inserting new OAuth user:", insertError);
        return _Error("Failed to create user record", 500);
      }
    } else {
      // Update existing user with OAuth login info
      const updateData: any = {
        updated_at: now,
        last_sign_in_at: now,
        last_sign_in_ip: clientIP,
      };

      // Update email confirmation if not already confirmed
      if (!existingUser.email_confirmed_at && user.email_confirmed_at) {
        updateData.email_confirmed_at = user.email_confirmed_at;
      }

      // Update profile information from OAuth provider if available
      if (user.user_metadata?.avatar_url && !existingUser.avatar_url) {
        updateData.avatar_url = user.user_metadata.avatar_url;
      }

      if ((user.user_metadata?.full_name || user.user_metadata?.name) && !existingUser.full_name) {
        updateData.full_name = user.user_metadata.full_name || user.user_metadata.name;
      }

      const { error: updateError } = await supabase
        .from("users")
        .update(updateData)
        .eq("id", user.id);

      if (updateError) {
        console.error("Error updating OAuth user:", updateError);
        return _Error("Failed to update user record", 500);
      }
    }

    // Log successful OAuth login for security monitoring
    try {
      await supabase
        .from("auth_logs")
        .insert({
          user_id: user.id,
          action: 'oauth_login',
          provider: provider,
          ip_address: clientIP,
          user_agent: req.headers.get('User-Agent') || 'unknown',
          created_at: now,
        });
    } catch (logError) {
      console.error("Failed to log OAuth login:", logError);
      // Don't fail the request for logging errors
    }

    return _Success({
      message: "OAuth callback processed successfully",
      user: {
        id: user.id,
        email: user.email,
        email_confirmed_at: user.email_confirmed_at,
        user_metadata: user.user_metadata,
      },
    });
  } catch (error: any) {
    console.error("OAuth callback error:", error);
    return _Error("An unexpected error occurred during OAuth callback", 500);
  }
}

// Add OPTIONS method for CORS preflight
export async function OPTIONS(req: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
