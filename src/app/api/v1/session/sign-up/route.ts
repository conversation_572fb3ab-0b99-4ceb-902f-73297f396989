import { _Error, _Success } from "@/lib/response";
import { supabase } from "@/lib/supabase/constants";
import { serviceRoleSupabase } from "@/lib/supabase/service-role";
import { NextRequest, NextResponse } from "next/server";

export const runtime = "nodejs";
export const dynamic = "force-dynamic";

const register = async (email: string, password: string, currentDomain: string) => {
  try {
    const normalizedEmail = email?.toLowerCase();
    
    // Check if user exists
    const { data: ifExists } = await supabase
      .from("users")
      .select("id")
      .eq("email", normalizedEmail)
      .single();

    if (ifExists) {
      return _Error("User already exists");
    }

    // Sign up user
    const { data, error } = await supabase.auth.signUp({
      email: normalizedEmail,
      password,
      options: {
        emailRedirectTo: `${currentDomain}/auth/sign-in`,
      },
    });

    console.log('🔍 Supabase signup response:', {
      hasUser: !!data?.user,
      userEmail: data?.user?.email,
      emailConfirmed: data?.user?.email_confirmed_at,
      confirmationSent: data?.user && !data?.user?.email_confirmed_at,
      error: error?.message
    });

    if (error) {
      return _Error(error.message);
    }

    if (data.user) {
      // Try to insert user record into public.users table
      // Use service role if available, otherwise skip (user can be created later via trigger)
      try {
        const { error: insertError } = await serviceRoleSupabase
          .from("users")
          .insert({
            id: data.user.id,
            email: normalizedEmail
          });

        if (insertError) {
          console.warn("Failed to insert user record:", insertError.message);
          // Don't fail the whole signup - user is created in auth.users
        } else {
          console.log("✅ User profile record created successfully");
        }
      } catch (error) {
        console.warn("User profile creation skipped - service role not available");
      }

      return data.user;
    }

    return data;
  } catch (error) {
    return _Error('Registration failed');
  }
};

export async function POST(req: NextRequest) {
  try {
    const { email, password } = (await req.json()) as { email: string; password: string };

    if (!email || !password) {
      return _Error("Email and password are required");
    }

    const host = req.headers.get("host");
    const protocol = req.headers.get("x-forwarded-proto") ?? "http";
    const currentDomain = `${protocol}://${host}`;

    const result = await register(email, password, currentDomain);
    if (result instanceof NextResponse) {
      return result;
    }
    return _Success(result);
  } catch (error) {
    return _Error("An unexpected error occurred");
  }
}
