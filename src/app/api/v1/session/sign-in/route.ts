import { _Error, _Success } from "@/lib/response";
import { supabase } from "@/lib/supabase/constants";
import { NextRequest, NextResponse } from "next/server";

export const runtime = "edge";
export const dynamic = "force-dynamic";

const login = async (email: string, password: string) => {
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    console.log('🔍 Sign-in attempt:', {
      email,
      hasUser: !!data?.user,
      emailConfirmed: data?.user?.email_confirmed_at,
      error: error?.message
    });

    if (error) {
      return _Error(error.message);
    }

    return data;
  } catch (error) {
    return _Error('Authentication failed');
  }
};

export async function POST(req: NextRequest) {
  try {
    const { email, password } = await req.json();
    if (!email || !password) {
      return _Error("Email and password are required");
    }
    
    const result = await login(email, password);
    if (result instanceof NextResponse) {
      return result;
    }
    
    const response = _Success(result);
    response.headers.set("Set-Cookie", `session=${result.session?.access_token}; Path=/; SameSite=Strict; Secure; Max-Age=86400`);
    response.headers.append("Set-Cookie", `refresh=${result.session?.refresh_token}; Path=/; SameSite=Strict; Secure; Max-Age=604800`);
    return response;
  } catch (error) {
    return NextResponse.error();
  }
}
