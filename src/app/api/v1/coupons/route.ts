import { NextRequest, NextResponse } from 'next/server'
import { validateCoupon } from '@/services/coupon.service'

export async function POST(req: NextRequest) {
  try {
    const { couponCode } = await req.json()
    if (!couponCode) {
      return NextResponse.json(
        { error: 'Coupon code is required' },
        { status: 400 }
      )
    }
    
    // Validate coupon from database
    const { data: coupon, error: couponError, isValid } = await validateCoupon(couponCode)

    if (!isValid || couponError) {
      return NextResponse.json(
        {
          valid: false,
          error: couponError || 'Invalid or used coupon code'
        },
        { status: 400 }
      )
    }
    
    // All coupons are FREE (100% discount)
    return NextResponse.json({
      valid: true,
      coupon: {
        id: coupon!.id,
        code: coupon!.coupon,
        isFree: true
      },
      message: 'Coupon valid! DApp listing will be FREE'
    })
    
  } catch (error: any) {
    console.error('Coupon validation error:', error)
    return NextResponse.json(
      { error: 'Failed to validate coupon' },
      { status: 500 }
    )
  }
}