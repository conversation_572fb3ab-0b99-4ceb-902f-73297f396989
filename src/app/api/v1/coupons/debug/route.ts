import { NextRequest, NextResponse } from 'next/server'
import { serviceRoleSupabase } from '@/lib/supabase/service-role'

export async function GET(req: NextRequest) {
  try {
    // Get all coupons with their status
    const { data: coupons, error } = await serviceRoleSupabase
      .from('coupons')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(50)

    if (error) {
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      )
    }

    // Get stats
    const stats = {
      total: coupons.length,
      applied: coupons.filter(c => c.is_applied).length,
      available: coupons.filter(c => !c.is_applied).length
    }

    return NextResponse.json({
      success: true,
      stats,
      coupons: coupons.map(c => ({
        id: c.id,
        coupon: c.coupon,
        is_applied: c.is_applied,
        user_id: c.user_id,
        dapp_id: c.dapp_id,
        created_at: c.created_at
      }))
    })
    
  } catch (error: any) {
    return NextResponse.json(
      { error: 'Failed to fetch coupons' },
      { status: 500 }
    )
  }
}
