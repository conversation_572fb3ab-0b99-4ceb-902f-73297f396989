import { NextRequest, NextResponse } from 'next/server'
import <PERSON><PERSON> from 'stripe'
import {
  addNewDapp,
  sendPaymentReceipt,
  updateDappSubscription,
} from '@/services/dapp.service'
import { applyCoupon } from '@/services/coupon.service'
import { HTTP_STATUS, ERROR_MESSAGES } from '@/constants'

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!)

export async function POST(req: NextRequest) {
  try {
    const body = await req.text()
    const signature = req.headers.get('stripe-signature')

    if (!signature || !process.env.STRIPE_WEBHOOK_SECRET) {
      console.error('❌ Webhook: Missing signature or secret')
      return NextResponse.json(
        { error: ERROR_MESSAGES.UNAUTHORIZED },
        { status: HTTP_STATUS.BAD_REQUEST }
      )
    }
    
    const event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET
    )

    console.log(`🔔 Webhook received: ${event.type} [${event.id}]`)
    console.log('🔍 Webhook Debug - Full Event:', {
      type: event.type,
      id: event.id,
      data: event.data.object
    })

    switch (event.type) {
      case 'checkout.session.completed':
        const session = event.data.object as Stripe.Checkout.Session
        console.log('🔍 Checkout Session Debug:', {
          hasMetadata: !!session?.metadata,
          hasPaymentIntent: !!session.payment_intent,
          metadata: session?.metadata,
          paymentIntent: session.payment_intent,
          sessionStatus: session.status
        })
        
        if (session?.metadata && session.payment_intent) {
          const paymentId = session.payment_intent as string
          const validTill = new Date(
            new Date().setFullYear(new Date().getFullYear() + 1)
          )


          
          const dappId = session?.metadata?.id
          
          try {
            const { data, error } = dappId
              ? await updateDappSubscription(dappId, validTill, paymentId)
              : await addNewDapp(session, validTill)

            if (error) {
              throw error
            }

            console.log('✅ DApp created successfully:', data);
            console.log('🕐 DApp creation timestamp:', new Date().toISOString());

            // Handle coupon usage if present
            if (session.metadata.couponId && session.metadata.couponId !== '' && session.metadata.userId) {
              try {
                await applyCoupon(session.metadata.couponId, session.metadata.userId, data.id)
              } catch (couponError) {
                console.error('⚠️ Coupon usage failed:', couponError)
                // Don't fail the whole process for coupon issues
              }
            }
            
            // Subscription info is already saved in the dapps table during creation

            // Try to send receipt email (optional)
            try {
              if (session.customer_details?.email) {
                await sendPaymentReceipt(session)
              }
            } catch (emailError) {
              // Don't fail the whole webhook for email issues
            }
          } catch (error) {
            console.error('❌ Failed to process DApp:', error)
            throw error
          }
        } else {
          console.warn('⚠️ No metadata or payment_intent in session')
        }
        break

      default:
        console.log(`ℹ️ Unhandled event type: ${event.type}`)
    }

    console.log(`✅ Webhook processed successfully: ${event.type}`)
    return NextResponse.json({ received: true })

  } catch (error) {
    console.error('❌ Webhook error:', error)
    return NextResponse.json(
      { error: ERROR_MESSAGES.SERVER_ERROR },
      { status: HTTP_STATUS.BAD_REQUEST }
    )
  }
}
