import Stripe from 'stripe'
import { NextResponse, NextRequest } from 'next/server'
import { getPricingPlanById, usdToCents } from '@/services/pricing.service'
import { validateCoupon, applyCoupon } from '@/services/coupon.service'
import { addNewDapp } from '@/services/dapp.service'
import { _Error, _Success } from '@/lib/response'

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!)

export async function POST(req: NextRequest) {
  try {
    const dappData = await req.json()
    
    const { data: pricingPlan, error: pricingError } = await getPricingPlanById(dappData.planId)
    
    if (pricingError || !pricingPlan) {
      return _Error('Invalid pricing plan selected', 400)
    }
    
    // Handle FREE coupon if provided
    if (dappData.couponCode) {
      const { data: coupon, error: couponError, isValid } = await validateCoupon(dappData.couponCode)
      
      if (!isValid || couponError) {
        return _Error(couponError || 'Invalid coupon code', 400)
      }
      
      // All coupons are FREE → Create DApp directly, bypass Stripe
      try {
        
        // Create DApp directly without payment
        const validTill = new Date(
          new Date().setFullYear(new Date().getFullYear() + 1)
        )
        
        // Create a mock session object for addNewDapp
        const mockSession = {
          metadata: {
            ...dappData,
            planId: dappData.planId.toString(),
            planType: pricingPlan.name,
            planPrice: '0', // FREE with coupon
            couponCode: coupon!.coupon,
            couponId: coupon!.id,
            couponUsed: 'true'
          },
          payment_intent: `coupon_${coupon!.id}` 
        }
        
        const { data: dappResult, error: dappError } = await addNewDapp(mockSession as any, validTill)

        if (dappError) {
          throw dappError
        }
        
        // Mark coupon as used
        const { error: useCouponError } = await applyCoupon(coupon!.id, dappData.userId, dappResult.id)

        if (useCouponError) {
          // Don't fail the whole process, DApp is already created
        }
        

        
        return _Success({
          freeWithCoupon: true,
          dappId: dappResult.id,
          couponUsed: coupon!.coupon,
          redirectUrl: `/payment-success?free=true&coupon=${coupon!.coupon}&dapp=${encodeURIComponent(dappData.dappName || '')}&plan=${encodeURIComponent(pricingPlan.name)}&dapp_id=${dappResult.id}`,
          planDetails: {
            name: pricingPlan.name,
            price: 0,
            originalPrice: pricingPlan.price_usd,
            features: pricingPlan.features,
            couponApplied: coupon!.coupon
          }
        })
        
      } catch (error: any) {
        return _Error('Failed to process coupon. Please try again.', 500)
      }
    }

    // Debug pricing calculation
    // planName: pricingPlan.name,
    // monthlyPrice: pricingPlan.price_usd,
    // annualPrice: pricingPlan.price_usd * 12,
    // billingPeriod: pricingPlan.billing_period

    const priceCents = usdToCents(pricingPlan.price_usd * 12)

    
    // Debug: Check logo URL before sending to Stripe
    const isValidHttpUrl = (url: string) => {
      try {
        const urlObj = new URL(url);
        return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
      } catch {
        return false;
      }
    };
    
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'usd',
            product_data: {
              name: `BNRY dApps - ${pricingPlan.name} Plan`,
              description: `Subscription for ${dappData.dappName}`,
              // Temporarily disable images to test
              // images: dappData.logo && isValidHttpUrl(dappData.logo) ? [dappData.logo] : undefined,
            },
            unit_amount: priceCents,
          },
          quantity: 1,
        },
      ],
      mode: 'payment',
      success_url: `${req.nextUrl.origin}/payment-success?session_id={CHECKOUT_SESSION_ID}&amount=${(pricingPlan.price_usd * 12).toFixed(2)}&plan=${encodeURIComponent(pricingPlan.name)}&dapp=${encodeURIComponent(dappData.dappName || '')}&dapp_id=PENDING`,
      cancel_url: `${req.nextUrl.origin}${dappData?.cancel_url || '/payment-failed'}`,
      metadata: {
        ...dappData, 
        planId: dappData.planId.toString(), 
        planType: pricingPlan.name, 
        planPrice: pricingPlan.price_usd.toString(),
        couponCode: dappData.couponCode || '',
        couponId: '',
        couponUsed: 'false'
      },
    })
    
    return _Success({ 
      id: session.id,
      sessionId: session.id, // For debugging
      url: session.url, // For debugging
      planDetails: {
        name: pricingPlan.name,
        price: pricingPlan.price_usd,
        features: pricingPlan.features
      }
    })
  } catch (error: any) {
    return _Error(error.message, 500)
  }
}