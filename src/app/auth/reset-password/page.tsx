"use client";
import React, { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { usePost } from "@/hooks/useFetcher";
import Link from "next/link";

const formSchema = z.object({
  email: z.string().email(),
});

const ResetPassword = () => {
  const [isSuccess, setIsSuccess] = useState(false);
  const [userEmail, setUserEmail] = useState("");
  const resetLinkFetcher = usePost();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    if (!values.email) {
      return;
    }

    const email = values.email.trim();
    setUserEmail(email);
    const result = await resetLinkFetcher.post("/api/v1/session/reset-link", { email });

    if (result.success) {
      setIsSuccess(true);
    }
    // Error handling is automatic via the fetcher hook
  }



  return (
    <div>
      <h1 className="font-protest-strike text-3xl leading-[1.3] uppercase text-primary">reset your password</h1>
      <p className="text-secondary-foreground text-xs font-medium mt-2 leading-[1.5]">
        {!isSuccess
          ? "Enter your email address and we'll send you a link to reset your password."
          : "Check your email for the reset link and follow the instructions."
        }
      </p>

      {!isSuccess ? (
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-7 my-7">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-normal text-secondary-foreground text-sm">Your email ID</FormLabel>
                  <div className="relative">
                    <FormControl className="relative">
                      <Input
                        {...field}
                        disabled={resetLinkFetcher.loading}
                      />
                    </FormControl>
                  </div>
                  <FormMessage className="text-xs" />
                </FormItem>
              )}
            />

            <Button
              type="submit"
              className="bg-green px-6 py-5 dark:text-white cursor-pointer"
              disabled={resetLinkFetcher.loading}
            >
              {resetLinkFetcher.loading ? "Sending..." : "Send Reset Link"}
            </Button>
          </form>
        </Form>
      ) : (
        <div className="mt-4 space-y-4">
          <div className="text-center">
            <p className="text-sm text-secondary-foreground mb-2">
              We've sent a reset link to <strong>{userEmail}</strong>
            </p>
            <p className="text-xs text-muted-foreground">
              Check your email and click the link to reset your password
            </p>
          </div>

          <div className="text-center space-y-3">
            <p className="text-xs text-muted-foreground">
              Didn't receive the email? Check your spam folder or
            </p>
            <Button
              variant="link"
              onClick={() => onSubmit({ email: userEmail })}
              disabled={resetLinkFetcher.loading}
              className="text-sm text-green p-0 h-auto"
            >
              {resetLinkFetcher.loading ? "Sending..." : "Resend Email"}
            </Button>
          </div>

          <div className="bg-muted/50 rounded-lg p-3 mt-4">
            <p className="text-xs text-muted-foreground text-center">
              ⏰ Reset links expire after 1 hour for security reasons
            </p>
          </div>
        </div>
      )}

      <div className="flex items-center justify-center text-xs my-7 gap-1">
        <p className="text-secondary-foreground">Remember your password?</p>
        <Link
          href="/auth/sign-in"
          className="font-semibold text-green"
        >
          Sign in here
        </Link>
      </div>
    </div>
  );
};

export default ResetPassword;
