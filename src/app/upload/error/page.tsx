"use client";

import Logo from "@/components/Logo";
import { But<PERSON> } from "@/components/ui/button";
import { useProfileStore } from "@/store/profile";
import Image from "next/image";
import Link from "next/link";
import { useEffect } from "react";

const Error = () => {
    const user = useProfileStore((state) => state.user);
    useEffect(() => {
        console.log("User in Error Page:", user);
    }, [user]);
  return (
    <div className="grid grid-cols-3 h-screen w-screen">
      <div className="col-span-1 relative">
        <Logo />
        <div className="size-full relative">
          <Image
            src="/congratulations_bg.webp"
            alt="Error Background"
            sizes="auto"
            fill
            className="object-cover object-bottom"
          />
        </div>
      </div>

      <div className="col-span-2 flex items-center justify-center">
        <div className="w-[520px] bg-card rounded-2xl px-6 mx-4 lg:px-[60px] py-[30px] shadow-xl">
          <h1 className="font-protest-strike text-3xl leading-[1.3] uppercase text-destructive">
            Something went wrong
          </h1>

          <p className="text-muted-foreground text-sm mt-2 leading-relaxed">
            We couldn’t complete your request, <span className="font-semibold text-foreground">User Name</span>.
          </p>

          <p className="text-secondary-foreground text-xs font-medium mt-2 leading-[1.5]">
            Please try again or contact support if the issue persists.
          </p>

          <div className="flex flex-col sm:flex-row gap-3 mt-7">
            <Link href="/dashboard">
              <Button variant="outline" className="h-11 px-6 w-full">
                Go to Dashboard
              </Button>
            </Link>
            <Link href="/support">
              <Button className="bg-destructive text-white h-11 px-6 w-full">
                Contact Support
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Error;
