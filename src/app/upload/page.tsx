"use client";
import Footer from "@/components/home/<USER>";
import Header from "@/components/home/<USER>";
import { Separator } from "@/components/ui/separator";
import { CouponInput } from "@/components/ui/coupon-input";
import { zodResolver } from "@hookform/resolvers/zod";
import clsx from "clsx";
import Image from "next/image";
import React, { useState, useEffect, useCallback } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Loader2, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Swiper, SwiperProps, SwiperSlide } from "swiper/react";
import "swiper/css";
import { Textarea } from "@/components/ui/textarea";
import { Check, ChevronsUpDown } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  generateSafeFilename,
  createStorageUrl,
  getStorageBaseUrl,
} from "@/lib/utils/image";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { createAuthenticatedClient } from "@/lib/supabase/client";
import { toast } from "sonner";
import { CATEGORIES } from "@/constants";
import { UPLOAD_CONSTANTS, type PaymentMethod, type UploadStep } from "@/constants/upload";
import { useRouter } from "next/navigation";
import useGetUser from "@/hooks/useGetUser";
import { stripePromise } from "@/lib/stripe";
import { walletStore } from "../../store/wallet.store";
import { connect, disconnect } from "@/services/wallet.service";
import { BNRY_ADDRESS } from "@/blockchain/blockchainConsts";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface PricingPlan {
  id: number;
  name: string;
  price_usd: number;
  billing_period: string;
  features: string[];
  is_active: boolean;
}

const swiperConfig: SwiperProps = {
  effect: "slide",
  slidesPerView: "auto",
  spaceBetween: 10,
  freeMode: true,
  grabCursor: true,
  breakpoints: {
    320: {
      slidesPerView: 1.1,
      spaceBetween: 8,
    },
    375: {
      slidesPerView: 1.3,
      spaceBetween: 10,
    },
    480: {
      slidesPerView: 1.6,
      spaceBetween: 12,
    },
    640: {
      slidesPerView: 2.2,
      spaceBetween: 16,
    },
    768: {
      slidesPerView: 2.8,
      spaceBetween: 16,
    },
  },
};

export interface User {
  id: string;
  email: string;
}



const formSchema = z.object({
  dappName: z
    .string()
    .min(UPLOAD_CONSTANTS.VALIDATION.DAPP_NAME_MIN, `DApp name must be at least ${UPLOAD_CONSTANTS.VALIDATION.DAPP_NAME_MIN} characters long`)
    .max(UPLOAD_CONSTANTS.VALIDATION.DAPP_NAME_MAX, `DApp name can't be longer than ${UPLOAD_CONSTANTS.VALIDATION.DAPP_NAME_MAX} characters`),
  category: z.string().min(1, "Please select a category for your DApp"),
  description: z
    .string()
    .min(UPLOAD_CONSTANTS.VALIDATION.DESCRIPTION_MIN, `Description must be at least ${UPLOAD_CONSTANTS.VALIDATION.DESCRIPTION_MIN} characters long`)
    .max(UPLOAD_CONSTANTS.VALIDATION.DESCRIPTION_MAX, `Description can't exceed ${UPLOAD_CONSTANTS.VALIDATION.DESCRIPTION_MAX} characters`),
  liveUrl: z
    .string()
    .url("Please provide a valid URL for your DApp's live version"),
  logo: z
    .instanceof(File, { message: "Logo is required. Please upload a logo." })
    .refine((file) => file.size <= UPLOAD_CONSTANTS.FILE_LIMITS.LOGO_MAX_SIZE, {
      message:
        `Logo file size must be under ${UPLOAD_CONSTANTS.FILE_LIMITS.LOGO_MAX_SIZE / (1024 * 1024)}MB. Please choose a smaller file.`,
    }),
});

const UploadDapp = () => {
  const [openDropdown, setOpenDropdown] = useState(false);
  const [avatarPreview, setAvatarPreview] = useState<string>("");
  const [uploadStep, setUploadStep] = useState<UploadStep>(1);
  const [selectedPlan, setSelectedPlan] = useState<PricingPlan | null>(null);
  const [pricingPlans, setPricingPlans] = useState<PricingPlan[]>([]);
  const [loadingPlans, setLoadingPlans] = useState(true);
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>(UPLOAD_CONSTANTS.PAYMENT_METHODS.CARD);
  const [appliedCoupon, setAppliedCoupon] = useState<any>(null);
  const [formData, setFormData] = useState<z.infer<typeof formSchema> | null>(
    null
  );
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [pricingFetched, setPricingFetched] = useState(false);
  const [showExchangeModal, setShowExchangeModal] = useState(false);
  const router = useRouter();

  const user = useGetUser();

  const {
    address,
    isInjected,
    isConnected,
    usdPrice,
    fetchUSDPrice,
    walletBalance,
    fetchWalletBalance,
  } = walletStore((state) => state);
  const connectWallet = () => {
    if (isInjected) {
      connect();
    }
  };
  
  const isFreeWithCoupon = useCallback(() => {
    return appliedCoupon?.isFree || false;
  }, [appliedCoupon?.isFree]);

  // Debug user state
  // Remove heavy debug logging - only log when needed
  // useEffect(() => {
  //   console.log('🔍 Upload Page Auth Debug:', {
  //     user: user?.user,
  //     loading: user?.loading,
  //     hasUser: !!user?.user,
  //     userId: user?.user?.id
  //   });
  // }, [user]);

  // Fetch pricing plans only once when user is authenticated
  useEffect(() => {
    let isMounted = true;

    const fetchPricingPlans = async () => {
      // Wait for user auth check to complete first
      if (user?.loading) return;

      // Only fetch if user is authenticated and not already fetched
      if (!user?.user || pricingFetched) return;

      if (!isMounted) return;

      setLoadingPlans(true);

      try {
        // Direct API call to avoid hook dependency issues
        const response = await fetch("/api/v1/pricing-plans", {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        });

        if (!isMounted) return;

        if (response.ok) {
          const result = await response.json();
          if (result.success && result.plans) {
            setPricingPlans(result.plans);
            // Set first plan as default selected
            if (result.plans.length > 0) {
              setSelectedPlan(result.plans[0]);
            }
            setPricingFetched(true); // Mark as fetched
          } else {
            toast.error("Failed to load pricing plans");
          }
        } else {
          toast.error("Failed to load pricing plans");
        }
      } catch (error) {
        if (isMounted) {
          console.error("Pricing plans fetch error:", error);
          toast.error("Failed to load pricing plans");
        }
      } finally {
        if (isMounted) {
          setLoadingPlans(false);
        }
      }
    };

    fetchPricingPlans();

    return () => {
      isMounted = false;
    };
  }, [user?.loading, user?.user, pricingFetched]); // Stable dependencies

  useEffect(() => {
    if (address) {
      fetchWalletBalance(address);
      if (usdPrice === null) {
        fetchUSDPrice();
      }
    }
  }, [address, fetchWalletBalance, fetchUSDPrice, usdPrice]);

  // Redirect to sign-in if not authenticated
  useEffect(() => {
    if (!user?.loading && !user?.user) {
      router.push("/auth/sign-in");
    }
  }, [user?.loading, user?.user, router]);

  useEffect(() => {
    if (isConnected && address && !isFreeWithCoupon()) {
      setPaymentMethod(UPLOAD_CONSTANTS.PAYMENT_METHODS.WALLET);
    } else if (!isConnected || isFreeWithCoupon()) {
      setPaymentMethod(UPLOAD_CONSTANTS.PAYMENT_METHODS.CARD);
    }
  }, [isConnected, address, appliedCoupon, isFreeWithCoupon]);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      dappName: "",
      category: "",
      description: "",
      liveUrl: "",
    },
  });

  const handleSelectAvatar = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setAvatarPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setFormData(values);
    setUploadStep(2);
  }

  const handleCouponApplied = (couponData: any) => {
    setAppliedCoupon(couponData);
    toast.success("🎉 Coupon applied! DApp listing is now FREE!");
  };

  const handleCouponRemoved = () => {
    setAppliedCoupon(null);
    toast.info("Coupon removed");
  };

  const handleCreateDapp = async (hash?: string) => {
    try {
      setIsLoading(true);

      const BUCKET_NAME = "dapp-logos";
      // Generate safe filename using utility function
      const fileName = generateSafeFilename(formData?.dappName || "dapp");

      // Create authenticated client for upload
      const authenticatedClient = await createAuthenticatedClient();

      const { data, error } = await authenticatedClient.storage
        .from(BUCKET_NAME)
        .upload(fileName, formData?.logo as File, {
          cacheControl: "3600",
          upsert: true,
        });

      if (error) {
        console.error("🚨 Upload Error:", error);
        return toast.error("Failed to upload logo. Please try again.");
      }

      console.log("✅ Upload Success:", data);

      const logoUrl = createStorageUrl(
        getStorageBaseUrl(),
        data?.fullPath || ""
      );

      // Fix double slash issue - remove any double slashes except after protocol
      let fixedLogoUrl = logoUrl.replace(/([^:]\/)\/+/g, "$1");

      // Fix double bucket name issue (e.g., /dapp-logos/dapp-logos/ -> /dapp-logos/)
      fixedLogoUrl = fixedLogoUrl.replace(
        "/dapp-logos/dapp-logos/",
        "/dapp-logos/"
      );

      if (selectedPlan?.price_usd && usdPrice) {
        fetch("/api/v1/wallet-payment", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            dappId: null, // No ID yet, will be created
            dappName: formData?.dappName,
            category: formData?.category,
            description: formData?.description,
            liveUrl: formData?.liveUrl,
            logo: fixedLogoUrl,
            planType: selectedPlan?.name,
            userId: user.user?.id,
            hash: hash || null,
            from: address,
            to: process.env.NEXT_PUBLIC_TO_ADDRESS,
            value: ((selectedPlan?.price_usd * UPLOAD_CONSTANTS.BLOCKCHAIN.ANNUAL_MULTIPLIER) / usdPrice).toFixed(UPLOAD_CONSTANTS.VALIDATION.DECIMAL_PLACES) || 0,
          }),
        })
          .then((res) => res.json())
          .then((data) => {
            console.log("✅ Wallet Payment Success:", data);
            toast.success(UPLOAD_CONSTANTS.MESSAGES.DAPP_SUBMIT_SUCCESS);

            // Prepare payment data for success page
            const paymentData = {
              amount: (selectedPlan?.price_usd * UPLOAD_CONSTANTS.BLOCKCHAIN.ANNUAL_MULTIPLIER).toFixed(2),
              transactionId: hash || `#WALLET_${Date.now()}`,
              paymentMethod: "Crypto Wallet",
              planName: selectedPlan?.name,
              dappName: formData?.dappName,
              isFree: false,
              dappId: data?.dappId?.toString(),
            };

            // Store payment data in session storage
            sessionStorage.setItem("paymentData", JSON.stringify(paymentData));

            // Redirect to payment success page
            router.push(
              `/payment-success?amount=${paymentData.amount}&plan=${encodeURIComponent(paymentData.planName || "")}&dapp=${encodeURIComponent(paymentData.dappName || "")}&dapp_id=${data?.dappId || ""}&session_id=${encodeURIComponent(hash || "")}`
            );
          })
          .catch((createError) => {
            console.error("🚨 Create DApp Error:", createError);
            toast.error("Failed to create DApp. Please try again.");
          });
      }
    } catch (error) {
      toast.error("Failed to submit DApp. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handlePayment = async () => {
    debugger;
    if (paymentMethod === UPLOAD_CONSTANTS.PAYMENT_METHODS.WALLET && selectedPlan && usdPrice) {
      window.enkrypted
        ?.sendTransaction({
          value:
            Number(((selectedPlan?.price_usd * UPLOAD_CONSTANTS.BLOCKCHAIN.ANNUAL_MULTIPLIER) / usdPrice).toFixed(UPLOAD_CONSTANTS.VALIDATION.DECIMAL_PLACES)) || 0,
          network: UPLOAD_CONSTANTS.BLOCKCHAIN.NETWORK,
          gas: UPLOAD_CONSTANTS.BLOCKCHAIN.GAS_TOKEN,
          token: UPLOAD_CONSTANTS.BLOCKCHAIN.PAYMENT_TOKEN,
          to: process.env.NEXT_PUBLIC_TO_ADDRESS,
        })
        .then(async (hash: string) => {
          await handleCreateDapp(hash);
        })
        .catch((e) => {
          console.error("🚨 Wallet Payment Error:", e);
          toast.error(UPLOAD_CONSTANTS.MESSAGES.PAYMENT_FAILED);
        });
    } else if (paymentMethod === UPLOAD_CONSTANTS.PAYMENT_METHODS.CARD) {
      if (!formData) {
        return;
      }

      const dappDataForPayment = {
        dappName: formData.dappName,
        category: formData.category,
        description: formData.description,
        liveUrl: formData.liveUrl,
        logo: formData.logo,
        planId: selectedPlan?.id,
        userId: user.user?.id,
        couponCode: appliedCoupon?.code, // Pass coupon code
      };

      await doCardPayment(dappDataForPayment);
    }
  };

  const doCardPayment = async (dappData: any) => {
    try {
      setIsLoading(true);

      // Upload logo first to get URL
      let logoUrl = "";
      if (formData?.logo) {
        try {
          const BUCKET_NAME = "dapp-logos";
          // Generate safe filename using utility function
          const fileName = generateSafeFilename(formData.dappName || "dapp");

          // Create authenticated client for upload
          const authenticatedClient = await createAuthenticatedClient();

          const { data, error } = await authenticatedClient.storage
            .from(BUCKET_NAME)
            .upload(fileName, formData.logo as File, {
              cacheControl: "3600",
              upsert: true,
            });

          if (error) {
            console.error("🚨 Card Payment Upload Error:", error);
            toast.error("Failed to upload logo. Please try again.");
            return;
          }

          console.log("✅ Card Payment Upload Success:", data);

          if (data) {
            logoUrl = createStorageUrl(getStorageBaseUrl(), data.fullPath);

            // Fix double slash issue - remove any double slashes except after protocol
            logoUrl = logoUrl.replace(/([^:]\/)\/+/g, "$1");

            // Fix double bucket name issue (e.g., /dapp-logos/dapp-logos/ -> /dapp-logos/)
            logoUrl = logoUrl.replace(
              "/dapp-logos/dapp-logos/",
              "/dapp-logos/"
            );
          } else {
            toast.error("Logo upload failed - no data returned");
            return;
          }
        } catch (uploadError) {
          toast.error("Logo upload failed. Please try again.");
          return;
        }
      }

      const checkoutData = {
        ...dappData,
        logo: logoUrl,
        success_url: `/payment-success`,
        cancel_url: `/payment-failed`,
      };

      // Send to checkout API using direct fetch
      if (!formData) {
        throw new Error("Form data is missing");
      }

      const checkoutPayload = {
        dappName: checkoutData.dappName,
        category: checkoutData.category,
        description: checkoutData.description,
        liveUrl: checkoutData.liveUrl,
        logo: logoUrl, // Use the uploaded logo URL instead of File object
        planId: checkoutData.planId,
        userId: checkoutData.userId,
        couponCode: checkoutData.couponCode,
      };

      const response = await fetch("/api/v1/checkout-session", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(checkoutPayload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create checkout session");
      }

      const result = await response.json();

      console.log("🔍 API Response Full:", result);
      console.log("🔍 API Response Status:", response.status);
      console.log(
        "🔍 API Response Headers:",
        Object.fromEntries(response.headers.entries())
      );
      console.log("🔍 API Response Data:", result.data);
      console.log("🔍 Is Free with Coupon?", result.data?.freeWithCoupon);

      // Check if it's free with coupon
      if (result.data?.freeWithCoupon) {
        console.log("🎉 Free with coupon - redirecting to payment success");
        toast.success(
          `🎉 DApp created for FREE with coupon ${result.data.couponUsed}!`
        );
        const paymentData = {
          amount: "FREE",
          transactionId: `#COUPON_${Date.now()}`,
          paymentMethod: "Free (Coupon)",
          planName: result.data.planDetails?.name || selectedPlan?.name,
          dappName: formData?.dappName,
          isFree: true,
          couponUsed: result.data.couponUsed,
          dappId: result.data.dappId?.toString(),
        };
        sessionStorage.setItem("paymentData", JSON.stringify(paymentData));

        router.push(
          `/payment-success?free=true&coupon=${result.data.couponUsed}&dapp=${encodeURIComponent(formData?.dappName || "")}&plan=${encodeURIComponent(result.data.planDetails?.name || "")}&dapp_id=${result.data.dappId}`
        );

        return;
      }

      // Regular Stripe checkout
      console.log("🔍 Checking for Stripe session ID:", result.data?.id);
      console.log("🔍 Checking for Stripe URL:", result.data?.url);
      console.log(
        "🔍 Full result data structure:",
        JSON.stringify(result.data, null, 2)
      );

      if (result.data?.id) {
        console.log("🔍 Redirecting to Stripe with session:", result.data.id);

        // Check if we have a direct URL
        if (result.data?.url) {
          console.log("✅ Direct URL available, redirecting to Stripe page");
          // Direct redirect to Stripe checkout
          window.location.href = result.data.url;
          return;
        }

        // Fallback to Stripe library
        const stripe = await stripePromise;
        if (stripe) {
          console.log("✅ Stripe loaded, redirecting...");
          const stripeResult = await stripe.redirectToCheckout({
            sessionId: result.data.id,
          });

          if (stripeResult.error) {
            console.error("❌ Stripe redirect error:", stripeResult.error);
            toast.error(stripeResult.error.message || "Redirect failed");
          }
        } else {
          console.error("❌ Stripe not loaded");
          toast.error("Payment system not loaded");
        }
      } else {
        console.error("❌ No session ID returned from API");
        toast.error("Payment setup failed. Please try again.");
      }
    } catch (error) {
      toast.error("Payment failed. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  // Show loading state while checking authentication (faster loading screen)
  if (user?.loading) {
    return (
      <div>
        <Header />
        <div className="container mb-10 my-10 lg:my-16">
          <div className="flex lg:flex-row flex-col h-full">
            {/* Left sidebar skeleton */}
            <div className="lg:block hidden w-[428px]">
              <div className="flex flex-col gap-6">
                <div className="flex flex-col gap-2">
                  <div className="w-20 h-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                  <div className="w-48 h-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                  <div className="w-full h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                  <div className="w-3/4 h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                </div>

                {/* Step indicators skeleton */}
                {[1, 2, 3].map((step) => (
                  <div
                    key={step}
                    className="border rounded-2xl bg-card p-5 flex gap-4"
                  >
                    <div className="size-10 bg-gray-200 dark:bg-gray-700 rounded-xl animate-pulse"></div>
                    <div className="flex flex-col gap-2 flex-1">
                      <div className="w-16 h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                      <div className="w-32 h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                      <div className="w-48 h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="lg:mx-14 xl:mx-32 w-px bg-border hidden lg:block"></div>

            {/* Main content skeleton */}
            <div className="flex-1">
              <div className="flex flex-col gap-4">
                <div className="w-64 h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                <div className="w-full h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                <div className="w-3/4 h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>

                {/* Form skeleton */}
                <div className="mt-8 space-y-6">
                  {/* Logo upload skeleton */}
                  <div>
                    <div className="w-12 h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2 animate-pulse"></div>
                    <div className="size-28 bg-gray-200 dark:bg-gray-700 rounded-2xl animate-pulse"></div>
                  </div>

                  {/* Form fields skeleton */}
                  {UPLOAD_CONSTANTS.UI.SKELETON_FIELDS.map((field) => (
                    <div key={field}>
                      <div className="w-20 h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2 animate-pulse"></div>
                      <div className="w-full h-10 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                    </div>
                  ))}

                  {/* Submit button skeleton */}
                  <div className="w-24 h-12 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  if (!user?.user) {
    return null;
  }

  const checkWalletStatus = () => {
    return isConnected && address ? (
      <>
        Available:{" "}
        <span
          className={`text-[#A6A6A7] font-medium 
          ${selectedPlan && usdPrice && Number(((selectedPlan?.price_usd * UPLOAD_CONSTANTS.BLOCKCHAIN.ANNUAL_MULTIPLIER) / usdPrice).toFixed(UPLOAD_CONSTANTS.VALIDATION.DECIMAL_PLACES)) > Number(walletBalance.toFixed(UPLOAD_CONSTANTS.VALIDATION.DECIMAL_PLACES)) && "text-red-500"}`}
        >
          {(walletBalance || 0.0).toFixed(UPLOAD_CONSTANTS.VALIDATION.DECIMAL_PLACES)} ${UPLOAD_CONSTANTS.BLOCKCHAIN.PAYMENT_TOKEN}
        </span>
        <span
          className="ml-2 text-green underline cursor-pointer!"
          onClick={() => {
            disconnect();
            setPaymentMethod(UPLOAD_CONSTANTS.PAYMENT_METHODS.CARD);
            toast.success(UPLOAD_CONSTANTS.MESSAGES.WALLET_DISCONNECT_SUCCESS);
          }}
        >
          Disconnect
        </span>
      </>
    ) : (
      <>
        <Button
          onClick={connectWallet}
          variant={"outline"}
          className="text-[#fb7f40] border-[#fb7f40]"
        >
          Connect Wallet
        </Button>
      </>
    );
  };

  return (
    <div>
      <Header />
      <div className="container mb-10 my-10 lg:my-16">
        <div className="flex lg:flex-row flex-col h-full">
          <div className="lg:block hidden w-[428px]">
            <div className="flex flex-col gap-6">
              <div className="flex flex-col gap-1">
                <div className="relative w-20 h-16">
                  <Image
                    src={"/social-media-communication-concept.svg"}
                    className="object-contain"
                    quality={100}
                    sizes="auto"
                    alt=""
                    fill
                  />
                </div>
                <p className="font-protest-strike text-xl uppercase">
                  Launch Your DApp in Minutes
                </p>
                <p className="text-secondary-foreground">
                  Get your decentralized app in front of 169.5M+ users with a
                  smooth 3-step onboarding process.
                </p>
              </div>

              <div
                className={clsx(
                  "border rounded-2xl bg-card p-5 flex gap-4",
                  uploadStep >= 1 && "border-green"
                )}
              >
                <div className="rounded-xl flex items-center justify-center size-10 min-w-10 min-h-10 border font-protest-strike">
                  1
                </div>
                <div className="flex flex-col text-secondary-foreground font-medium gap-1">
                  <p className="text-sm">Step 1</p>
                  <p className="font-protest-strike text-foreground">
                    Submit Your DApp Info
                  </p>
                  <p className="text-sm">Add your dApp&apos;s essentials.</p>
                </div>
              </div>

              <div
                className={clsx(
                  "border rounded-2xl bg-card p-5 flex gap-4",
                  uploadStep >= 2 && "border-green"
                )}
              >
                <div className="rounded-xl flex items-center justify-center size-10 min-w-10 min-h-10 border font-protest-strike">
                  2
                </div>
                <div className="flex flex-col text-secondary-foreground font-medium gap-1">
                  <p className="text-sm">Step 2</p>
                  <p className="font-protest-strike text-foreground">
                    Choose a Subscription Plan
                  </p>
                  <p className="text-sm">
                    Choose a subscription that fits your scale and audience
                    reach.
                  </p>
                </div>
              </div>

              <div
                className={clsx(
                  "border rounded-2xl bg-card p-5 flex gap-4",
                  uploadStep >= 3 && "border-green"
                )}
              >
                <div className="rounded-xl flex items-center justify-center size-10 min-w-10 min-h-10 border font-protest-strike">
                  3
                </div>
                <div className="flex flex-col text-secondary-foreground font-medium gap-1">
                  <p className="text-sm">Step 3</p>
                  <p className="font-protest-strike text-foreground">
                    Make Payment & Go Live
                  </p>
                  <p className="text-sm">
                    Complete the payment via Crypto or Card, and launch
                    instantly.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <Separator
            orientation="vertical"
            className="!h-[540px] w-0 md:mx-14 xl:mx-32 border my-auto rounded-full hidden lg:block"
          />

          <div className="flex-1">
            {uploadStep === 1 && (
              <div>
                <div className="flex flex-col gap-2">
                  <p className="font-protest-strike text-xl lg:text-3xl uppercase">
                    Submit Your DApp Info
                  </p>
                  <p className="text-sm font-medium text-secondary-foreground">
                    Enter your dApp&apos;s essential details — category, name,
                    description, logo, and URL. This helps us showcase your dApp
                    effectively on our platform.
                  </p>
                </div>

                <div className="lg:mt-8 mt-2">
                  <Form {...form}>
                    <form
                      onSubmit={form.handleSubmit(onSubmit)}
                      className="space-y-4 mt-7"
                    >
                      <FormField
                        control={form.control}
                        name="logo"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel
                              className="font-normal text-secondary-foreground cursor-pointer text-sm"
                              htmlFor="avatar"
                            >
                              Logo
                            </FormLabel>
                            {!avatarPreview ? (
                              <label
                                htmlFor="avatar"
                                className={`size-[76px] lg:size-28 border rounded-2xl cursor-pointer dark:bg-input/30 bg-input ${
                                  isLoading
                                    ? "pointer-events-none opacity-50"
                                    : ""
                                }`}
                              >
                                <FormControl>
                                  <Input
                                    type="file"
                                    className="hidden"
                                    id="avatar"
                                    accept="image/*"
                                    onChange={(e) => {
                                      handleSelectAvatar(e);
                                      if (e.target.files && e.target.files[0]) {
                                        field.onChange(e.target.files[0]);
                                      }
                                    }}
                                    onBlur={field.onBlur}
                                    name={field.name}
                                    ref={field.ref}
                                    disabled={isLoading}
                                  />
                                </FormControl>
                              </label>
                            ) : (
                              <div
                                className={`group size-[76px] lg:size-28 border rounded-2xl cursor-pointer relative overflow-hidden ${
                                  isLoading
                                    ? "pointer-events-none opacity-50"
                                    : ""
                                }`}
                              >
                                <div
                                  className="group-hover:opacity-100 transition-all opacity-0 absolute top-0 left-0 size-full flex items-center justify-center z-10 bg-black/20"
                                  onClick={
                                    isLoading
                                      ? undefined
                                      : () => {
                                          setAvatarPreview("");
                                          field.onChange(undefined);
                                        }
                                  }
                                >
                                  <Trash2 className="text-red-500" />
                                </div>
                                <Image
                                  src={avatarPreview}
                                  sizes="auto"
                                  alt=""
                                  fill
                                  className="object-cover"
                                />
                              </div>
                            )}

                            <FormMessage className="text-xs" />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="dappName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="font-normal text-secondary-foreground text-sm">
                              Dapp name
                            </FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                className="h-10"
                                disabled={isLoading}
                              />
                            </FormControl>
                            <FormMessage className="text-xs" />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="category"
                        render={() => (
                          <div className="flex flex-col gap-2">
                            <FormLabel className="font-normal text-secondary-foreground text-sm">
                              Category
                            </FormLabel>
                            <Popover
                              open={openDropdown}
                              onOpenChange={
                                isLoading ? undefined : setOpenDropdown
                              }
                            >
                              <PopoverTrigger asChild>
                                <Button
                                  variant="outline"
                                  role="combobox"
                                  aria-expanded={openDropdown}
                                  className="w-[200px] justify-between"
                                  disabled={isLoading}
                                >
                                  {form.getValues("category") ||
                                    "Select category..."}
                                  <ChevronsUpDown className="opacity-50" />
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent className="w-[200px] p-0">
                                <Command>
                                  <CommandList>
                                    <CommandEmpty>
                                      No framework found.
                                    </CommandEmpty>
                                    <CommandGroup>
                                      {CATEGORIES.map((framework) => (
                                        <CommandItem
                                          key={framework.value}
                                          value={framework.value}
                                          onSelect={
                                            isLoading
                                              ? undefined
                                              : (currentValue) => {
                                                  form.setValue(
                                                    "category",
                                                    currentValue ===
                                                      form.getValues("category")
                                                      ? ""
                                                      : currentValue
                                                  );
                                                  form.clearErrors("category");
                                                  setOpenDropdown(false);
                                                }
                                          }
                                          className={
                                            isLoading
                                              ? "pointer-events-none opacity-50"
                                              : ""
                                          }
                                        >
                                          {framework.label}
                                          <Check
                                            className={cn(
                                              "ml-auto",
                                              form.getValues("category") ===
                                                framework.value
                                                ? "opacity-100"
                                                : "opacity-0"
                                            )}
                                          />
                                        </CommandItem>
                                      ))}
                                    </CommandGroup>
                                  </CommandList>
                                </Command>
                              </PopoverContent>
                            </Popover>
                            <FormMessage className="text-xs" />
                          </div>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="description"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="font-normal text-secondary-foreground text-sm">
                              Description
                            </FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="Tell us a little bit about yourself"
                                className="resize-none"
                                {...field}
                                disabled={isLoading}
                              />
                            </FormControl>
                            <FormMessage className="text-xs" />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="liveUrl"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="font-normal text-secondary-foreground text-sm">
                              Live URL
                            </FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                className="h-10"
                                disabled={isLoading}
                              />
                            </FormControl>
                            <FormMessage className="text-xs" />
                          </FormItem>
                        )}
                      />
                      <Button
                        type="submit"
                        className="bg-green px-6 py-5 dark:text-white cursor-pointer dark:hover:bg-secondary"
                        disabled={isLoading}
                      >
                        {isLoading ? "Loading..." : "Next"}
                      </Button>
                    </form>
                  </Form>
                </div>
              </div>
            )}

            {uploadStep === 2 && (
              <div className="flex flex-col gap-5 w-full">
                <div className="flex flex-col gap-1">
                  <p className="uppercase text-xl lg:text-3xl font-protest-strike">
                    Choose a Subscription Plan
                  </p>
                  <p className="text-sm lg:text-base text-secondary-foreground">
                    Select from our tailored yearly plans based on your usage
                    needs. Each plan is built to support scale and visibility.
                  </p>
                </div>

                <div className="hidden lg:grid grid-cols-3 gap-4 mt-10 w-full">
                  {loadingPlans ? (
                    <div className="col-span-3 text-center py-8">
                      <p>Loading pricing plans...</p>
                    </div>
                  ) : (
                    pricingPlans.map((plan, index) => {
                      const colors = [
                        {
                          bg: "bg-[#BCECFA]",
                          border: "border-[#42D4FF]",
                          icon: "/icons/6206114_24648 [Converted]-01.svg",
                        },
                        {
                          bg: "bg-[#C0EA7C]",
                          border: "border-[#2FC890]",
                          icon: "/icons/6206114_24648 [Converted]-02.svg",
                        },
                        {
                          bg: "bg-[#D7D9FF]",
                          border: "border-[#9B9DFF]",
                          icon: "/icons/6206114_24648 [Converted]-03.svg",
                        },
                      ];
                      const color = colors[index] || colors[0];

                      return (
                        <div
                          key={plan.id}
                          className={clsx(
                            "flex items-center min-w-[150px] cursor-pointer rounded-2xl h-16",
                            color.bg,
                            selectedPlan?.id === plan.id
                              ? `border-[2px] ${color.border}`
                              : "border"
                          )}
                          onClick={() => setSelectedPlan(plan)}
                        >
                          <div className="relative size-12">
                            <Image
                              src={color.icon}
                              alt=""
                              sizes="auto"
                              className="object-contain"
                              fill
                            />
                          </div>
                          <p className="text-primary dark:text-[#3E434E] text-lg font-bold">
                            {plan.name}
                          </p>
                        </div>
                      );
                    })
                  )}
                </div>

                <div className="lg:hidden">
                  <div className="relative">
                    {/* Swipe hint */}
                    <p className="text-xs text-gray-500 mb-2 text-center sm:hidden animate-pulse">
                      👈 Swipe to see all plans 👉
                    </p>
                    <Swiper {...swiperConfig}>
                    {loadingPlans ? (
                      <div className="text-center py-8">
                        <p>Loading pricing plans...</p>
                      </div>
                    ) : (
                      pricingPlans.map((plan, index) => {
                        const colors = [
                          {
                            bg: "bg-[#BCECFA]",
                            border: "border-[#42D4FF]",
                            icon: "/icons/6206114_24648 [Converted]-01.svg",
                          },
                          {
                            bg: "bg-[#C0EA7C]",
                            border: "border-[#2FC890]",
                            icon: "/icons/6206114_24648 [Converted]-02.svg",
                          },
                          {
                            bg: "bg-[#D7D9FF]",
                            border: "border-[#9B9DFF]",
                            icon: "/icons/6206114_24648 [Converted]-03.svg",
                          },
                        ];
                        const color = colors[index] || colors[0];

                        return (
                          <SwiperSlide key={plan.id} className="!w-auto">
                            <div
                              className={clsx(
                                "flex items-center min-w-[140px] sm:min-w-[160px] w-full cursor-pointer rounded-2xl h-16 px-4",
                                color.bg,
                                selectedPlan?.id === plan.id
                                  ? `border-[2px] ${color.border}`
                                  : "border"
                              )}
                              onClick={() => setSelectedPlan(plan)}
                            >
                              <div className="relative size-12">
                                <Image
                                  src={color.icon}
                                  alt=""
                                  sizes="auto"
                                  className="object-contain"
                                  fill
                                />
                              </div>
                              <p className="text-primary dark:text-[#3E434E] text-lg font-bold">
                                {plan.name}
                              </p>
                            </div>
                          </SwiperSlide>
                        );
                      })
                    )}
                    </Swiper>

                    {/* Scroll indicator for mobile */}
                    <div className="flex justify-center mt-2 sm:hidden">
                      <div className="flex space-x-1">
                        <div className="w-1 h-1 bg-gray-300 rounded-full"></div>
                        <div className="w-1 h-1 bg-gray-300 rounded-full"></div>
                        <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  {selectedPlan && (
                    <div
                      className={clsx(
                        "p-8 rounded-2xl border text-sm relative z-10",
                        selectedPlan.name === "Start" &&
                          "border-[#42D4FF] bg-[#BCECFA] text-primary",
                        selectedPlan.name === "Scale" &&
                          "border-[#2FC890] text-white",
                        selectedPlan.name === "Conquer" &&
                          "border-[#9B9DFF] bg-[#D7D9FF] text-primary"
                      )}
                      style={
                        selectedPlan.name === "Scale"
                          ? {
                              background:
                                "linear-gradient(180deg, #013D29 0%, #00BD77 100%)",
                              boxShadow:
                                "0px 68px 19px 0px rgba(26, 162, 101, 0.01), 0px 44px 17px 0px rgba(26, 162, 101, 0.07), 0px 25px 15px 0px rgba(26, 162, 101, 0.23), 0px 11px 11px 0px rgba(26, 162, 101, 0.38), 0px 3px 6px 0px rgba(26, 162, 101, 0.44)",
                            }
                          : {}
                      }
                    >
                      <div className="absolute size-full top-0 left-0 z-0">
                        <Image
                          src={"/dark_price.svg"}
                          alt=""
                          sizes="auto"
                          fill
                          className="object-cover"
                        />
                      </div>

                      <div
                        className={clsx(
                          "flex flex-col gap-[18px] relative z-10",
                          (selectedPlan.name === "Start" ||
                            selectedPlan.name === "Conquer") &&
                            "dark:text-[#3E434E]"
                        )}
                      >
                        <p>{selectedPlan.name}</p>
                        <p className="flex gap-2 items-end">
                          <span className="font-protest-strike text-3xl">
                            ${selectedPlan.price_usd}
                          </span>
                          <span>Per {selectedPlan.billing_period}</span>
                        </p>
                        <Separator
                          className={clsx(
                            selectedPlan.name === "Scale"
                              ? "bg-white/20"
                              : "bg-primary/20 dark:bg-[#3E434E]"
                          )}
                        />
                        {selectedPlan.features.map((feature, index) => (
                          <p
                            key={index}
                            className={clsx(
                              selectedPlan.name === "Scale"
                                ? "text-white"
                                : "text-secondary-foreground dark:text-[#3E434E]"
                            )}
                          >
                            {feature}
                          </p>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                <Button
                  onClick={() => {
                    setUploadStep(3);
                  }}
                  variant={"outline"}
                  className="w-full h-11 dark:border-green border-green text-green font-medium"
                >
                  {selectedPlan
                    ? `Pay $${Math.max(selectedPlan.price_usd * UPLOAD_CONSTANTS.BLOCKCHAIN.ANNUAL_MULTIPLIER, UPLOAD_CONSTANTS.VALIDATION.MIN_PAYMENT_AMOUNT).toFixed(2)} - billed annual`
                    : "Select a plan"}
                </Button>
              </div>
            )}

            {uploadStep === 3 && (
              <div className="flex flex-col gap-7 w-full">
                <div className="flex flex-col gap-1">
                  <p className="uppercase text-xl lg:text-3xl font-protest-strike">
                    Make Payment & Go Live
                  </p>
                  <p className="text-sm lg:text-base text-secondary-foreground">
                    Complete the payment using credit card or crypto wallet.
                    Your DApp is now published and live on BNRY DAPPS and
                    visible to millions via the One Wave app.
                  </p>
                </div>

                {/* Coupon Input Section */}
                <div className="p-6 border rounded-2xl bg-card">
                  <CouponInput
                    onCouponApplied={handleCouponApplied}
                    onCouponRemoved={handleCouponRemoved}
                  />
                </div>

                {/* FREE Notice when coupon applied */}
                {isFreeWithCoupon() && (
                  <div className="p-4 bg-green-50 border border-green-200 rounded-2xl">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-2xl">🎉</span>
                      <h3 className="font-semibold text-green-800">
                        DApp Listing is FREE!
                      </h3>
                    </div>
                    <p className="text-sm text-green-700">
                      With your coupon <strong>{appliedCoupon?.code}</strong>,
                      no payment is required. Just click the button below to
                      create your DApp instantly!
                    </p>
                  </div>
                )}

                <Separator />

                <div className="flex flex-col gap-7">
                  <div className="flex flex-col gap-2">
                    <div
                      className={clsx(
                        "p-6 rounded-2xl cursor-pointer border flex items-center gap-4",
                        paymentMethod === UPLOAD_CONSTANTS.PAYMENT_METHODS.WALLET && "border-green",
                        isFreeWithCoupon() && "opacity-50 cursor-not-allowed",
                        selectedPlan &&
                          usdPrice &&
                          walletBalance <
                            (selectedPlan?.price_usd * UPLOAD_CONSTANTS.BLOCKCHAIN.ANNUAL_MULTIPLIER) / usdPrice &&
                          !isFreeWithCoupon() &&
                          "cursor-default!"
                      )}
                      onClick={() =>
                        !isFreeWithCoupon() &&
                        selectedPlan &&
                        usdPrice &&
                        walletBalance >=
                          (selectedPlan?.price_usd * UPLOAD_CONSTANTS.BLOCKCHAIN.ANNUAL_MULTIPLIER) / usdPrice &&
                        setPaymentMethod(UPLOAD_CONSTANTS.PAYMENT_METHODS.WALLET)
                      }
                    >
                      <div className="size-12 relative">
                        <Image
                          src={"/055aa591dcdef9ef3f97d1b22916b1ab563b30b7.png"}
                          alt=""
                          sizes="auto"
                          fill
                        />
                      </div>

                      <div>
                        <p
                          className={clsx(
                            "font-semibold",
                            isFreeWithCoupon() && "text-muted-foreground"
                          )}
                        >
                          Wallet payment {isFreeWithCoupon() && "(Not needed)"}
                        </p>
                        <p className="text-sm text-secondary-foreground">
                          {!isInjected ? (
                            <>
                              {UPLOAD_CONSTANTS.MESSAGES.WALLET_INSTALL}{" "}
                              <a
                                href={UPLOAD_CONSTANTS.LINKS.ENKRYPTED_EXTENSION}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-green underline cursor-pointer hover:text-green/80 transition-colors active:text-green/60"
                              >
                                {UPLOAD_CONSTANTS.MESSAGES.WALLET_INSTALL_LINK}
                              </a>
                            </>
                          ) : (
                            checkWalletStatus()
                          )}
                        </p>
                      </div>
                    </div>

                    {!isFreeWithCoupon() &&
                      selectedPlan?.price_usd &&
                      usdPrice &&
                      (selectedPlan?.price_usd * UPLOAD_CONSTANTS.BLOCKCHAIN.ANNUAL_MULTIPLIER) / usdPrice >
                        walletBalance && (
                        <p className="text-secondary-foreground text-sm">
                          {UPLOAD_CONSTANTS.MESSAGES.REQUIRED_BALANCE}{" "}
                          {((selectedPlan?.price_usd * UPLOAD_CONSTANTS.BLOCKCHAIN.ANNUAL_MULTIPLIER) / usdPrice).toFixed(
                            UPLOAD_CONSTANTS.VALIDATION.DECIMAL_PLACES
                          )}{" "}
                          ${UPLOAD_CONSTANTS.BLOCKCHAIN.PAYMENT_TOKEN}{" "}
                          <span
                            className="text-green cursor-pointer hover:underline"
                            onClick={() => setShowExchangeModal(true)}
                          >
                            {UPLOAD_CONSTANTS.MESSAGES.BUY_BNRY_LINK}
                          </span>
                        </p>
                      )}
                  </div>

                  <div
                    className={clsx(
                      "p-6 rounded-2xl cursor-pointer border flex items-center gap-4",
                      paymentMethod === UPLOAD_CONSTANTS.PAYMENT_METHODS.CARD && "border-green",
                      isFreeWithCoupon() && "opacity-50 cursor-not-allowed"
                    )}
                    onClick={() =>
                      !isFreeWithCoupon() && setPaymentMethod(UPLOAD_CONSTANTS.PAYMENT_METHODS.CARD)
                    }
                  >
                    <div className="size-12 relative">
                      <Image
                        src={"/88302330e61fece43bfb86b2a6278f9f082b5816.png"}
                        alt=""
                        sizes="auto"
                        fill
                      />
                    </div>

                    <div>
                      <p
                        className={clsx(
                          "font-semibold",
                          isFreeWithCoupon() && "text-muted-foreground"
                        )}
                      >
                        Card payment {isFreeWithCoupon() && "(Not needed)"}
                      </p>
                    </div>
                  </div>

                  <Button
                    disabled={isLoading}
                    className={clsx(
                      "h-12 font-semibold text-lg",
                      isFreeWithCoupon()
                        ? "bg-green-600 hover:bg-green-700 text-white"
                        : "text-green border !border-[#22bc6b]"
                    )}
                    variant={isFreeWithCoupon() ? "default" : "outline"}
                    onClick={handlePayment}
                  >
                    {isLoading && <Loader2 className="animate-spin mr-2" />}
                    {isFreeWithCoupon()
                      ? UPLOAD_CONSTANTS.MESSAGES.FREE_DAPP_BUTTON
                      : `Proceed to Payment (${selectedPlan ? `$${Math.max(selectedPlan.price_usd * UPLOAD_CONSTANTS.BLOCKCHAIN.ANNUAL_MULTIPLIER, UPLOAD_CONSTANTS.VALIDATION.MIN_PAYMENT_AMOUNT).toFixed(2)}` : '$0.00'})`}
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Exchange Selection Modal */}
      <Dialog open={showExchangeModal} onOpenChange={setShowExchangeModal}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-center text-xl font-semibold">
              Select your exchange
            </DialogTitle>
            <p className="text-center text-sm text-muted-foreground mt-2">
              To buy the $BNRY select the exchange listed below.
            </p>
          </DialogHeader>

          <div className="flex flex-col gap-4 mt-6">
            <div className="grid grid-cols-2 gap-4">
              {/* MEXC Exchange */}
              <div
                className="p-6 border rounded-lg cursor-pointer hover:border-primary transition-colors flex flex-col items-center gap-3 bg-card hover:bg-accent"
                onClick={() => {
                  window.open(UPLOAD_CONSTANTS.LINKS.MEXC_EXCHANGE, "_blank");
                  setShowExchangeModal(false);
                }}
              >
                <div className={`${UPLOAD_CONSTANTS.EXCHANGE.MEXC.SIZE} ${UPLOAD_CONSTANTS.EXCHANGE.MEXC.COLOR} rounded-lg flex items-center justify-center`}>
                  <span className="text-white font-bold text-lg">{UPLOAD_CONSTANTS.EXCHANGE.MEXC.NAME}</span>
                </div>
              </div>

              {/* BingX Exchange */}
              <div
                className="p-6 border rounded-lg cursor-pointer hover:border-primary transition-colors flex flex-col items-center gap-3 bg-card hover:bg-accent"
                onClick={() => {
                  window.open(UPLOAD_CONSTANTS.LINKS.BINGX_EXCHANGE, "_blank");
                  setShowExchangeModal(false);
                }}
              >
                <div className={`${UPLOAD_CONSTANTS.EXCHANGE.BINGX.SIZE} ${UPLOAD_CONSTANTS.EXCHANGE.BINGX.COLOR} rounded-lg flex items-center justify-center`}>
                  <span className="text-white font-bold text-lg">{UPLOAD_CONSTANTS.EXCHANGE.BINGX.NAME}</span>
                </div>
              </div>
            </div>

            <Button
              variant="outline"
              onClick={() => setShowExchangeModal(false)}
              className="mt-4"
            >
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      <Footer />
    </div>
  );
};

export default UploadDapp;
