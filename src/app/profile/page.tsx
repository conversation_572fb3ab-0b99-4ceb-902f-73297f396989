"use client";
import Header from "@/components/home/<USER>";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import Image from "next/image";
import React, { useEffect, useState, useCallback } from "react";
import Link from "next/link";
import useGetUser from "@/hooks/useGetUser";
import { useDAppsApi, type DApp } from "@/hooks/useApi";
import { AlertCircle, Plus, ChevronLeft, ChevronRight } from "lucide-react";
import { getSafeImageSrc } from "@/lib/utils/image";
import moment from "moment";
import { useRouter } from "next/navigation";
import slugify from "slugify";

const DappCard = ({ dapp }: { dapp: DApp }) => {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      day: '2-digit',
      month: '2-digit',
      year: '2-digit'
    });
  };

  const formatTimeSpent = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`;
  };

  return (
    <Link
      href={`/dapps/${slugify(dapp.name || 'unnamed-dapp', { lower: true, strict: true })}/${dapp.id}`}
      className="block"
    >
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow cursor-pointer">
        {/* Header with logo and name */}
        <div className="flex items-center gap-3 mb-4">
          <div className="w-16 h-16 rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-700 flex-shrink-0">
            {getSafeImageSrc(dapp.logo) ? (
              <Image
                src={getSafeImageSrc(dapp.logo)!}
                alt={dapp.name || 'DApp logo'}
                width={64}
                height={64}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center text-gray-400 dark:text-gray-500 text-xs font-medium">
                No Logo
              </div>
            )}
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-gray-900 dark:text-white truncate">{dapp.name || 'Unnamed DApp'}</h3>
            <div className="flex items-center gap-2">
              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200 uppercase">
                {dapp.category || 'Uncategorized'}
              </span>
            </div>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Total views</p>
            <p className="text-sm font-medium text-gray-900 dark:text-white">{dapp.total_views || 0}</p>
          </div>
          <div>
            <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Avg time spent</p>
            <p className="text-sm font-medium text-gray-900 dark:text-white">
              {formatTimeSpent(dapp.avg_time_spend || 0)}
            </p>
          </div>
        </div>

        {/* Listed on and Status */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Listed on</p>
            <p className="text-sm font-medium text-gray-900 dark:text-white">
              {dapp.created_at ? formatDate(dapp.created_at) : 'N/A'}
            </p>
          </div>
          <div>
            <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Status</p>
            <p className="text-sm font-medium text-gray-900 dark:text-white">Active</p>
          </div>
        </div>
      </div>
    </Link>
  );
};

const Profile = () => {
  const [dapps, setDapps] = useState<DApp[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [totalCount, setTotalCount] = useState<number>(0);

  const ITEMS_PER_PAGE = 8;
  const { user, loading: userLoading } = useGetUser();
  const { list: dappsApi } = useDAppsApi();
  const router = useRouter();

  const fetchDapps = useCallback(async (page: number = currentPage) => {
    // Wait for auth to finish loading before checking user
    if (userLoading) {
      return;
    }

    if (!user?.id) {
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    const result = await dappsApi.execute({
      page,
      limit: ITEMS_PER_PAGE,
      // user_id is now handled server-side from session
    });



    if (result.success && result.data) {
      // Handle both old and new API response structures
      if (Array.isArray(result.data)) {
        // Old structure: { data: [...] }
        setDapps(result.data || []);
        setTotalCount(result.data.length);
        setTotalPages(1);
      } else if (result.data.data && result.data.pagination) {
        // New structure: { data: [...], pagination: {...} }
        setDapps(result.data.data || []);
        setTotalCount(result.data.pagination?.total || 0);
        setTotalPages(result.data.pagination?.totalPages || 1);
      } else {
        // Fallback
        setDapps([]);
        setTotalCount(0);
        setTotalPages(1);
      }
      setCurrentPage(page);
      setError(null);


    } else {
      if (result.error?.includes("User not found or session expired") ||
          result.error?.includes("Authentication required")) {
        console.log('🔄 Authentication error detected, redirecting to login...');
        router.push("/auth/sign-in");
        return;
      } else {
        setError(result.error || "Failed to load your dapps");
        setDapps([]);
        setTotalCount(0);
        setTotalPages(1);
      }
    }

    setLoading(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user?.id, userLoading, currentPage, ITEMS_PER_PAGE]); // Added userLoading dependency

  const handleRefresh = () => {
    fetchDapps(currentPage);
  };

  const handlePageChange = (page: number) => {
    fetchDapps(page);
  };

  useEffect(() => {
    if (!userLoading && user?.id) {
      fetchDapps(1);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user?.id, userLoading]); // Wait for auth loading to complete

  // Redirect to sign-in if not authenticated
  useEffect(() => {
    if (!userLoading && !user?.id) {
      console.log('🔄 User not authenticated, redirecting to login...');
      router.push("/auth/sign-in");
    }
  }, [userLoading, user?.id, router]);

  // Don't render if not authenticated
  if (!userLoading && !user?.id) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />

      <div className="container mx-auto px-4 py-8 max-w-7xl">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          <div className="flex flex-col justify-center">
            <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-4 uppercase">
              MY DAPPS DASHBOARD
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-8 leading-relaxed">
              Track performance, manage subscriptions, view analytics, and update your DApps — all from one streamlined
              dashboard built for creators.
            </p>
            <div className="flex gap-4">
              <Link href="/upload">
                <Button className="bg-teal-500 hover:bg-teal-600 text-white px-6 py-3 rounded-lg font-medium">
                  Upload your dApp
                </Button>
              </Link>
            </div>
          </div>

          <div className="flex justify-center items-center">
            <div className="relative w-80 h-80">
              <Image
                src="/194126321_15df01d8-bc6b-4e27-a1b0-8c854bef200d copy 1.svg"
                alt="Dashboard illustration"
                fill
                className="object-contain"
              />
            </div>
          </div>
        </div>

        <div className="mb-8">
          {error && (
            <Alert className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950 mb-6">
              <AlertCircle className="h-4 w-4 text-red-600 dark:text-red-400" />
              <AlertDescription className="text-red-800 dark:text-red-200 flex items-center justify-between">
                <span>{error}</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRefresh}
                  disabled={loading}
                  className="ml-4 text-red-600 border-red-300 hover:bg-red-100 dark:text-red-400 dark:border-red-600 dark:hover:bg-red-900"
                >
                  Try Again
                </Button>
              </AlertDescription>
            </Alert>
          )}

          {loading && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {Array.from({ length: 4 }).map((_, index) => (
                <div key={index} className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                  <div className="flex items-center gap-3 mb-4">
                    <Skeleton className="w-16 h-16 rounded-lg" />
                    <div className="flex-1">
                      <Skeleton className="h-5 w-3/4 mb-2" />
                      <Skeleton className="h-4 w-1/2" />
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Skeleton className="h-3 w-full mb-1" />
                        <Skeleton className="h-4 w-2/3" />
                      </div>
                      <div>
                        <Skeleton className="h-3 w-full mb-1" />
                        <Skeleton className="h-4 w-2/3" />
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Skeleton className="h-3 w-full mb-1" />
                        <Skeleton className="h-4 w-2/3" />
                      </div>
                      <div>
                        <Skeleton className="h-3 w-full mb-1" />
                        <Skeleton className="h-4 w-1/2" />
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {!loading && !error && dapps.length === 0 && (
            <div className="flex flex-col items-center justify-center py-16 px-4 text-center">
              <div className="w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-6">
                <Plus className="w-12 h-12 text-gray-400" />
              </div>
              <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">No dApps uploaded yet</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-8 max-w-md">
                Start building your dApp portfolio by uploading your first decentralized application.
              </p>
              <Link href="/upload">
                <Button className="bg-teal-500 hover:bg-teal-600 text-white">
                  <Plus className="w-4 h-4 mr-2" />
                  Upload your first dApp
                </Button>
              </Link>
            </div>
          )}

          {/* Dapps Grid */}
          {!loading && !error && dapps.length > 0 && (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {dapps.map((dapp) => (
                  <DappCard key={dapp.id} dapp={dapp} />
                ))}
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex flex-col sm:flex-row items-center justify-between mt-8 pt-6 border-t border-gray-200 dark:border-gray-700 gap-4">
                  <div className="text-sm text-gray-600 dark:text-gray-400 order-2 sm:order-1">
                    Showing {((currentPage - 1) * ITEMS_PER_PAGE) + 1} to {Math.min(currentPage * ITEMS_PER_PAGE, totalCount || dapps.length)} of {totalCount || dapps.length} dApps
                  </div>

                  <div className="flex items-center gap-2 order-1 sm:order-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1 || loading}
                      className="flex items-center gap-1"
                    >
                      <ChevronLeft className="w-4 h-4" />
                      <span className="hidden sm:inline">Previous</span>
                    </Button>

                    <div className="flex items-center gap-1">
                      {Array.from({ length: Math.min(5, totalPages || Math.ceil(dapps.length / ITEMS_PER_PAGE)) }, (_, i) => {
                        const calculatedTotalPages = totalPages || Math.ceil(dapps.length / ITEMS_PER_PAGE);
                        let pageNum;
                        if (calculatedTotalPages <= 5) {
                          pageNum = i + 1;
                        } else if (currentPage <= 3) {
                          pageNum = i + 1;
                        } else if (currentPage >= calculatedTotalPages - 2) {
                          pageNum = calculatedTotalPages - 4 + i;
                        } else {
                          pageNum = currentPage - 2 + i;
                        }

                        return (
                          <Button
                            key={pageNum}
                            variant={currentPage === pageNum ? "default" : "outline"}
                            size="sm"
                            onClick={() => handlePageChange(pageNum)}
                            disabled={loading}
                            className="w-8 h-8 p-0"
                          >
                            {pageNum}
                          </Button>
                        );
                      })}
                    </div>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === (totalPages || Math.ceil(dapps.length / ITEMS_PER_PAGE)) || loading}
                      className="flex items-center gap-1"
                    >
                      <span className="hidden sm:inline">Next</span>
                      <ChevronRight className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </div>

        {/* Footer Section */}
        <div className="mt-16 pt-8 border-t border-gray-200 dark:border-gray-700">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div className="flex items-center gap-2">
              <div className="w-6 h-6 bg-gray-900 dark:bg-white rounded flex items-center justify-center">
                <span className="text-white dark:text-gray-900 text-xs font-bold">B</span>
              </div>
              <div>
                <p className="font-semibold text-gray-900 dark:text-white">BNRY</p>
                <p className="text-xs text-gray-500 dark:text-gray-400">Dapps</p>
              </div>
            </div>

            <div className="text-right">
              <p className="text-sm font-medium text-gray-900 dark:text-white">LEGAL</p>
              <div className="flex gap-4 mt-1">
                <Link href="/terms" className="text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                  Terms of service
                </Link>
                <Link href="/privacy" className="text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                  Privacy policy
                </Link>
              </div>
            </div>
          </div>

          <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
            <div className="flex flex-col md:flex-row justify-between items-center gap-4">
              <p className="text-xs text-gray-500 dark:text-gray-400">
                &copy; {moment().format("YYYY")} Binary Dapps. All rights reserved.
              </p>
              <div className="flex gap-4">
                <Link href="#" className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"/>
                  </svg>
                </Link>
                <Link href="#" className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                  </svg>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;
