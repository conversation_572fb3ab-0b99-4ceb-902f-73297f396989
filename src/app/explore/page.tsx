"use client";

import GameFi from "@/components/explore/Gamefi";
import Hero from "@/components/explore/Hero";
import TopDApps from "@/components/explore/TopDApps";
import Trending from "@/components/explore/Trending";
import Footer from "@/components/home/<USER>";
import PopularDapps from "@/components/home/<USER>";
import { useSearchParams } from "next/navigation";

const Explore = () => {
  const searchParams = useSearchParams();
  const headerParam = searchParams.get('header');
  const showHeader = headerParam !== 'false';

  // Debug logging
  console.log('🔍 Explore page - header param:', headerParam);
  console.log('🔍 Explore page - showHeader:', showHeader);

  return (
    <div>
      <Hero showHeader={showHeader} />
      <Trending />
      <PopularDapps />
      <TopDApps />
      <GameFi />
      {showHeader && <Footer />}
    </div>
  );
};

export default Explore;
