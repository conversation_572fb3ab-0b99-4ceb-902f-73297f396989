"use client";
import Footer from "@/components/home/<USER>";
import Header from "@/components/home/<USER>";
import React from "react";
import { useParams } from "next/navigation";
import Trending from "@/components/explore/Trending";
import PopularDapps from "@/components/home/<USER>";
import TopDApps from "@/components/explore/TopDApps";
import GameFi from "@/components/explore/Gamefi";
import { CategoryType } from "@/constants";

const Category = () => {
  const params = useParams<{ category: CategoryType }>();

  const renderBody = () => {
    switch (params.category) {
      case CategoryType.Trending:
        return <Trending />;
      case CategoryType.PopularDapps:
        return <PopularDapps />;
      case CategoryType.TopDApps:
        return <TopDApps />;
      case CategoryType.GameFi:
        return <GameFi />;
      default:
        return <></>;
    }
  };

  return (
    <div>
      <Header />
      {renderBody()}
      <Footer />
    </div>
  );
};

export default Category;
