"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Header from "@/components/home/<USER>";
import Footer from "@/components/home/<USER>";
import { supabaseClient } from "@/lib/supabase/client";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import Image from "next/image";
import { getSafeImageSrc } from "@/lib/utils/image";

interface DAppRow {
  rank: string;
  name: string;
  image: string;
  category: string;
  totalViews: number;
  avgTimeSpend: number;
}

const TopDAppsPage = () => {
  const router = useRouter();
  const [data, setData] = useState<DAppRow[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const fetchDapps = async () => {
      try {
        setLoading(true);
        
        // Fetch top 20 DApps ordered by total_views
        const { data: dappsData } = await supabaseClient
          .from("dapps")
          .select("*")
          .order('total_views', { ascending: false })
          .limit(20);

        if (dappsData) {
          const formattedData = dappsData.map((dapp, index) => ({
            rank: (index + 1).toString(),
            name: dapp.name,
            image: dapp.logo,
            category: dapp.category,
            totalViews: dapp.total_views || 0,
            avgTimeSpend: dapp.avg_time_spend || 0,
          }));
          setData(formattedData);
        }
      } catch (error) {
        console.error("Error fetching top dapps:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchDapps();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />
      
      <div className="container lg:my-24 my-4">
        {/* Header Section */}
        <div className="mb-8">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push('/explore')}
            className="flex items-center gap-2 mb-4"
          >
            <ArrowLeft className="w-4 h-4" />
            Back
          </Button>
          <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white uppercase font-protest-strike">
            Top DApps
          </h1>
        </div>

        {/* Data Table */}
        <div className="bg-white dark:bg-gray-800 rounded-lg overflow-x-auto">
          {loading ? (
            <div className="space-y-4 p-6">
              {Array.from({ length: 20 }).map((_, index) => (
                <div key={index} className="flex items-center space-x-4 p-4 border rounded">
                  <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                  <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                    <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-1/2" />
                  </div>
                  <div className="w-20 h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                  <div className="w-20 h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                </div>
              ))}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Rank</TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Total Views</TableHead>
                  <TableHead>Avg time spend</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                  {data.map((row) => (
                  <TableRow key={row.rank}>
                    <TableCell className="text-secondary-foreground">#{row.rank}</TableCell>
                    <TableCell>
                      <div className="flex gap-3 items-center">
                        <div className="relative size-16 rounded-xl overflow-hidden">
                          {getSafeImageSrc(row.image) ? (
                            <Image
                              src={getSafeImageSrc(row.image)!}
                              alt={row.name}
                              sizes="auto"
                              fill
                              className="object-cover"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500 text-xs font-medium">
                              No Logo
                            </div>
                          )}
                        </div>
                        <div className="font-semibold">{row.name}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary">{row.category}</Badge>
                    </TableCell>
                    <TableCell>{row.totalViews}</TableCell>
                    <TableCell>{row.avgTimeSpend}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default TopDAppsPage;
