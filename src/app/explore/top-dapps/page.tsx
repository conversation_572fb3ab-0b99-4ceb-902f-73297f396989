"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Header from "@/components/home/<USER>";
import Footer from "@/components/home/<USER>";
import { supabaseClient } from "@/lib/supabase/client";
import { DataTable } from "@/components/ui/data-table";
import { columns, Row } from "@/components/explore/TopDApps";

const TopDAppsPage = () => {
  const router = useRouter();
  const [data, setData] = useState<Row[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const fetchDapps = async () => {
      try {
        setLoading(true);
        
        // Fetch all DApps ordered by total_views
        const { data: dappsData } = await supabaseClient
          .from("dapps")
          .select("*")
          .order('total_views', { ascending: false })
          .limit(100);

        if (dappsData) {
          const formattedData = dappsData.map((dapp, index) => ({
            rank: (index + 1).toString(),
            name: dapp.name,
            image: dapp.logo,
            category: dapp.category,
            totalViews: dapp.total_views || 0,
            avgTimeSpend: dapp.avg_time_spend || 0,
          }));
          setData(formattedData);
        }
      } catch (error) {
        console.error("Error fetching top dapps:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchDapps();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />
      
      <div className="container lg:my-24 my-4">
        {/* Header Section */}
        <div className="mb-8">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push('/explore')}
            className="flex items-center gap-2 mb-4"
          >
            <ArrowLeft className="w-4 h-4" />
            Back
          </Button>
          <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white uppercase font-protest-strike">
            Top DApps
          </h1>
        </div>

        {/* Data Table */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
          {loading ? (
            <div className="space-y-4">
              {Array.from({ length: 10 }).map((_, index) => (
                <div key={index} className="flex items-center space-x-4 p-4 border rounded">
                  <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                  <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                    <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-1/2" />
                  </div>
                  <div className="w-20 h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                  <div className="w-20 h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                </div>
              ))}
            </div>
          ) : (
            <DataTable columns={columns} data={data} />
          )}
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default TopDAppsPage;
