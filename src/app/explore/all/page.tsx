"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft, ChevronRight, ArrowLeft } from "lucide-react";
import Header from "@/components/home/<USER>";
import Footer from "@/components/home/<USER>";
import DAppCard, { DApp } from "@/components/home/<USER>";
import { Skeleton } from "@/components/ui/skeleton";
import { Separator } from "@/components/ui/separator";


const ITEMS_PER_PAGE = 100;

const AllDAppsPage = () => {
  const router = useRouter();
  const [dapps, setDapps] = useState<DApp[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [totalItems, setTotalItems] = useState<number>(0);
  const [activeCategory, setActiveCategory] = useState<string>("All");

  const categories = ["All", "DeFi", "NFT", "Games", "Tools", "Social", "Multi-chain"];


  useEffect(() => {
    const fetchDapps = async () => {
      try {
        setLoading(true);
        console.log('Fetching DApps for page:', currentPage);

        const category = activeCategory === "All" ? "All" : activeCategory;
        const res = await fetch(`/api/v1/dapps/explore?category=${category}&page=${currentPage}&limit=${ITEMS_PER_PAGE}`);
        const response = await res.json();

        console.log('API Response:', response);

        if (response.data) {
          setDapps(response.data.dapps || []);
          setTotalPages(response.data.pagination.totalPages);
          setTotalItems(response.data.pagination.totalItems);
        } else {
          console.error('API returned error:', response);
        }
      } catch (error) {
        console.error("Error fetching dapps:", error);
        // Set empty state on error
        setDapps([]);
        setTotalPages(1);
        setTotalItems(0);
      } finally {
        setLoading(false);
      }
    };

    fetchDapps();
  }, [currentPage, activeCategory]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleCategoryChange = (category: string) => {
    setActiveCategory(category);
    setCurrentPage(1); // Reset to first page when changing category
  };



  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />
      
      <div className="container lg:my-24 my-4">
        {/* Header Section */}
        <div className="mb-8">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push('/explore')}
            className="flex items-center gap-2 mb-4"
          >
            <ArrowLeft className="w-4 h-4" />
            Back
          </Button>
          <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white uppercase font-protest-strike">
            All DApps
          </h1>

          {/* Category Tabs */}
          <div className="flex flex-wrap gap-2 mt-6">
            {categories.map((category) => (
              <Button
                key={category}
                variant={activeCategory === category ? "default" : "outline"}
                size="sm"
                onClick={() => handleCategoryChange(category)}
                className={`px-4 py-2 transition-colors ${
                  activeCategory === category
                    ? "bg-primary text-primary-foreground"
                    : "text-primary border-primary hover:bg-primary hover:text-white"
                }`}
              >
                {category}
              </Button>
            ))}
          </div>
        </div>



        {/* DApps Grid */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {Array.from({ length: ITEMS_PER_PAGE }).map((_, index) => (
              <div key={index} className="border p-5 rounded-2xl bg-card flex flex-col gap-3">
                <div className="flex gap-3">
                  <Skeleton className="size-16 min-w-16 min-h-16 rounded-xl" />
                  <div className="flex flex-col gap-2 w-full">
                    <Skeleton className="h-6 w-3/4" />
                    <Skeleton className="h-4 w-1/2" />
                  </div>
                </div>
                <Separator />
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Skeleton className="h-4 w-full mb-1" />
                    <Skeleton className="h-5 w-2/3" />
                  </div>
                  <div>
                    <Skeleton className="h-4 w-full mb-1" />
                    <Skeleton className="h-5 w-2/3" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : dapps.length > 0 ? (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {dapps.map((dapp) => (
                <DAppCard key={dapp.id} {...dapp} />
              ))}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-end mt-12 gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1 || loading}
                  className="flex items-center gap-1"
                >
                  <ChevronLeft className="w-4 h-4" />
                  Previous
                </Button>

                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }

                    return (
                      <Button
                        key={pageNum}
                        variant={currentPage === pageNum ? "default" : "outline"}
                        size="sm"
                        onClick={() => handlePageChange(pageNum)}
                        disabled={loading}
                        className="w-8 h-8 p-0"
                      >
                        {pageNum}
                      </Button>
                    );
                  })}
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages || loading}
                  className="flex items-center gap-1"
                >
                  Next
                  <ChevronRight className="w-4 h-4" />
                </Button>
              </div>
            )}
          </>
        ) : (
          <div className="flex flex-col items-center justify-center py-16 text-center">
            <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">No DApps Found</h3>
            <p className="text-gray-600 dark:text-gray-400 mb-8 max-w-md">
              We couldn't find any DApps at the moment. Please try again later.
            </p>
          </div>
        )}
      </div>

      <Footer />
    </div>
  );
};

export default AllDAppsPage;
