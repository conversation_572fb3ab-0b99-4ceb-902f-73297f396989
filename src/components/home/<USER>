"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import clsx from "clsx";
import { useEffect, useState, useCallback } from "react";
import Link from "next/link";
import { Skeleton } from "@/components/ui/skeleton";
import DAppCard, { DApp } from "@/components/home/<USER>";
import { Swiper, SwiperProps, SwiperSlide } from "swiper/react";
import "swiper/css";
import { supabaseClient } from "@/lib/supabase/client";
import { Separator } from "../ui/separator";
import { useRouter } from "next/navigation";

// Keep UI labels as in Figma design - Added "All" tab
const filterOptions = ["All", "DeFi", "NFT", "Games", "Tools", "Social", "Multi-chain", "Wallet", "Security"];

const swiperConfig: SwiperProps = {
  effect: "slide",
  slidesPerView: 1.1,
  centeredSlides: true,
  spaceBetween: 10,
  loop: true,
};

const PopularDapps = () => {
  const [active, setActive] = useState<string>("All");
  const [allDapps, setAllDapps] = useState<DApp[]>([]); // Store all DApps
  const [loading, setLoading] = useState<boolean>(true);

  const [store, setStore] = useState<Record<string, DApp[] | null>>({
    All: null,
    DeFi: null,
    NFT: null,
    Games: null,
    Tools: null,
    Social: null,
    "Multi-chain": null,
  });

  const fetchFilteredDapps = useCallback(async () => {
    // Fetch DApps based on the selected category (limit to 8 for homepage)
    const req = supabaseClient.from("dapps").select("*");
    if (active !== "All") {
      req.eq("category", active);
    }
    req.limit(8).overrideTypes<DApp[]>();
    const { data } = await req;
    setStore((prevStore) => ({ ...prevStore, [active]: data || [] }));
    setAllDapps(data || []);
  }, [active]);

  useEffect(() => {
    setAllDapps([]); // Reset all DApps
    if (!store[active]) {
      try {
        setLoading(true);
        fetchFilteredDapps();
      } catch (error) {
        console.error("Error fetching dapps:", error);
      } finally {
        setLoading(false);
      }
    } else {
      // If already fetched, use cached data
      setAllDapps(store[active]);
    }
  }, [active, store, fetchFilteredDapps]);

  // Initial load - fetch "All" category with limit 8
  useEffect(() => {
    if (!store["All"]) {
      fetchFilteredDapps();
    }
  }, [fetchFilteredDapps, store]);

  return (
    <div className="container lg:my-24 my-4">
      <div className="flex flex-col gap-2">
        <div className="flex justify-between items-center lg:items-start">
          <div className="flex items-center justify-center">
            <h2 className="uppercase font-protest-strike text-xl lg:text-3xl text-primary">Our popular Dapps</h2>
          </div>
        </div>

      </div>

      <div className="flex mt-5 justify-between items-center gap-4">
        <div className="flex overflow-x-scroll gap-2 md:gap-4">
          {filterOptions.map((option) => (
            <Button
              onClick={() => setActive(option)}
              key={option}
              variant={"outline"}
              className={clsx(
                "h-10 min-w-[105px] text-secondary-foreground/80 px-4 rounded-xl",
                active === option &&
                  "hover:bg-primary hover:text-white bg-primary text-white dark:!bg-white dark:text-background dark:hover:text-background font-semibold"
              )}
            >
              {option}
            </Button>
          ))}
        </div>

        <Link href={active === "All" ? "/explore/all" : `/explore/${active}`}>
          <Button
            variant="outline"
            className="px-6 py-2 text-primary border-primary hover:bg-primary hover:text-white transition-colors font-semibold whitespace-nowrap"
          >
            View All
          </Button>
        </Link>
      </div>

      <div className="mt-10 grid-cols-1 lg:grid-cols-4 gap-3 md:gap-5 hidden md:grid">
        {!loading ? (
          allDapps.length > 0 ? (
            allDapps.map((dapp) => {
              return <DAppCard key={dapp.id} {...dapp} />;
            })
          ) : (
            // Empty state
            <div className="col-span-4 flex flex-col items-center justify-center py-16 text-center">
              <div className="mb-4 text-6xl opacity-20">🔍</div>
              <h3 className="text-xl font-semibold mb-2">No {active} DApps Found</h3>
              <p className="text-secondary-foreground mb-4">We couldn't find any DApps in the {active} category yet.</p>
              <div className="flex gap-2">
                <Button variant="outline" onClick={() => setActive("All")} className="mt-2">
                  Browse All DApps
                </Button>
              </div>
            </div>
          )
        ) : (
          Array.from({ length: active === "All" ? 12 : 8 }).map((_, index) => (
            <div key={index} className="border p-5 rounded-2xl bg-card flex flex-col gap-3">
              <div className="flex gap-3">
                <Skeleton className="size-16 min-w-16 min-h-16 rounded-xl relative overflow-hidden" />
                <div className="flex flex-col gap-2 w-full">
                  <Skeleton className="h-1/2 w-full rounded-full" />
                  <Skeleton className="h-1/2 w-full rounded-full" />
                </div>
              </div>

              <Separator className="my-1" />

              <div className="grid grid-cols-2 gap-14">
                <div className="flex flex-col gap-1">
                  <p className="text-secondary-foreground text-sm">Total views</p>
                  <Skeleton className="h-5 w-full rounded-md" />
                </div>
                <div className="flex flex-col gap-1">
                  <p className="text-secondary-foreground text-sm">Avg time spend</p>
                  <Skeleton className="h-5 w-full rounded-md" />
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      <div className="mt-10 md:hidden">
        {!loading ? (
          allDapps.length > 0 ? (
            <Swiper {...swiperConfig}>
              {allDapps.map((dapp) => {
                return (
                  <SwiperSlide key={dapp.id}>
                    <DAppCard {...dapp} />
                  </SwiperSlide>
                );
              })}
            </Swiper>
          ) : (
            // Mobile empty state
            <div className="flex flex-col items-center justify-center py-16 text-center">
              <div className="mb-4 text-6xl opacity-20">🔍</div>
              <h3 className="text-xl font-semibold mb-2">No {active} DApps Found</h3>
              <p className="text-secondary-foreground mb-4 text-sm">
                We couldn't find any DApps in the {active} category yet.
              </p>
              <Button variant="outline" onClick={() => setActive("All")} className="mt-2" size="sm">
                Browse All DApps
              </Button>
            </div>
          )
        ) : (
          // Mobile loading state
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="border p-5 rounded-2xl bg-card flex flex-col gap-3">
                <div className="flex gap-3">
                  <Skeleton className="size-16 min-w-16 min-h-16 rounded-xl relative overflow-hidden" />
                  <div className="flex flex-col gap-2 w-full">
                    <Skeleton className="h-1/2 w-full rounded-full" />
                    <Skeleton className="h-1/2 w-full rounded-full" />
                  </div>
                </div>
                <Separator className="my-1" />
                <div className="grid grid-cols-2 gap-14">
                  <div className="flex flex-col gap-1">
                    <p className="text-secondary-foreground text-sm">Total views</p>
                    <Skeleton className="h-5 w-full rounded-md" />
                  </div>
                  <div className="flex flex-col gap-1">
                    <p className="text-secondary-foreground text-sm">Avg time spend</p>
                    <Skeleton className="h-5 w-full rounded-md" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default PopularDapps;
