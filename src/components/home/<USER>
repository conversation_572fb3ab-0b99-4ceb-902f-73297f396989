"use client";
import React from "react";
import { But<PERSON> } from "../ui/button";
import { Moon, Sun } from "lucide-react";
import { useTheme } from "next-themes";

const ToggleTheme = () => {
  const { setTheme, theme } = useTheme();

  return (
    <Button
      onClick={() => {
        (() => (theme === "dark" ? setTheme("light") : setTheme("dark")))();
      }}
      variant="outline"
      size="icon"
      className="size-11 rounded-xl cursor-pointer flex"
    >
      <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
      <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
    </Button>
  );
};

export default ToggleTheme;
