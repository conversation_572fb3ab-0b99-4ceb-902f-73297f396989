"use client";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { useTheme } from "next-themes";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { AlignJustify, Moon, Sun, X } from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useState, useCallback } from "react";
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet";
import ToggleTheme from "./ToggleTheme";
import { toast } from "sonner";
import clsx from "clsx";
import useGetUser from "@/hooks/useGetUser";
import { API_ENDPOINTS, AUTH_ROUTES, SUCCESS_MESSAGES, ERROR_MESSAGES } from "@/constants";
import { getUserAvatarUrl } from "@/lib/auth/utils";

export interface JwtPayload {
  email: string;
  iat: number;
  exp: number;
}

const Header = () => {
  const { user, setUser } = useGetUser();
  const [isShowSheet, setIsShowSheet] = useState(false);

  const { setTheme, theme } = useTheme();
  const router = useRouter();

  const handleSignOut = useCallback(async () => {
    try {
      const response = await fetch(API_ENDPOINTS.AUTH.SIGN_OUT);

      if (response.ok) {
        setUser(null);
        toast.success(SUCCESS_MESSAGES.SIGN_OUT_SUCCESS);
        router.push(AUTH_ROUTES.SIGN_IN);
      } else {
        toast.error(ERROR_MESSAGES.SOMETHING_WENT_WRONG);
      }
    } catch (error) {
      toast.error(ERROR_MESSAGES.SOMETHING_WENT_WRONG);
    }
  }, [setUser, router]);

  return (
    <div className="bg-background/30 backdrop-blur-sm relative z-50">
      <div className="container py-[10px] flex justify-between items-center">
        <Link href={"/"} className="text-xl md:text-2xl font-protest-strike text-primary">
          BNRY <br />
          dAPPS
        </Link>

        <div className="flex items-center gap-2 lg:gap-4">
          <ToggleTheme />

          {!user ? (
            <Link href={AUTH_ROUTES.SIGN_IN} className="hidden lg:block">
              <Button className="h-11 min-w-24">Login</Button>
            </Link>
          ) : (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <div className="size-11 rounded-xl border border-input bg-background cursor-pointer lg:flex hidden relative overflow-hidden">
                  <Image src={getUserAvatarUrl(user)} alt="User avatar" sizes="auto" fill className="object-cover" />
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-64 rounded-2xl" align="end">
                <DropdownMenuItem
                  onClick={() => router.push("/profile")}
                  className="font-normal cursor-pointer text-primary py-2 px-4"
                >
                  <div className="size-5 relative">
                    <Image src={"/globe-04.png"} alt="" sizes="auto" fill quality={100} />
                  </div>
                  View on dashboard
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => router.push("/auth/reset-password")}
                  className="font-normal cursor-pointer text-primary py-2 px-4"
                >
                  <div className="size-5 relative">
                    <Image src={"/key-01.png"} alt="" sizes="auto" fill quality={100} />
                  </div>
                  Change password
                </DropdownMenuItem>
                <DropdownMenuItem className="font-normal cursor-pointer text-primary py-2 px-4" onClick={handleSignOut}>
                  <div className="size-5 relative">
                    <Image src={"/log-out-02.png"} alt="" sizes="auto" fill quality={100} />
                  </div>
                  Log out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}

          {user ? (
            <>
              <Button
                variant={"outline"}
                className="h-11 text-green border-green cursor-pointer"
                onClick={() => router.push("/upload")}
              >
                Upload Dapps
              </Button>

              <Sheet open={isShowSheet} onOpenChange={setIsShowSheet}>
                <SheetTrigger onClick={() => setIsShowSheet(true)}>
                  <AlignJustify size={35} className="lg:hidden" />
                </SheetTrigger>
                <SheetContent>
                  <SheetHeader className="hidden">
                    <SheetTitle className="hidden" />
                  </SheetHeader>

                  <div className={clsx("p-5 flex flex-col gap-4 h-full", !user ? "justify-between" : "justify-start")}>
                    <div className="flex items-center min-w-[150px] justify-between">
                      <X size={40} onClick={() => setIsShowSheet(false)} />
                      <Button
                        onClick={() => {
                          (() => (theme === "dark" ? setTheme("light") : setTheme("dark")))();
                        }}
                        variant="outline"
                        size="icon"
                        className="size-11 rounded-xl cursor-pointer"
                      >
                        <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
                        <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
                      </Button>
                    </div>

                    {user ? (
                      <div className="flex flex-col gap-3 mt-5">
                        <div
                          onClick={() => router.push("/profile")}
                          className="font-normal cursor-pointer flex items-center gap-2 text-primary py-2 px-2"
                        >
                          <div className="size-5 relative">
                            <Image src={"/globe-04.png"} alt="" sizes="auto" fill quality={100} />
                          </div>
                          View on dashboard
                        </div>

                        <Link
                          href={"/auth/reset-password"}
                          className="font-normal cursor-pointer flex items-center gap-2 text-primary py-2 px-2"
                        >
                          <div className="size-5 relative">
                            <Image src={"/key-01.png"} alt="" sizes="auto" fill quality={100} />
                          </div>
                          Change password
                        </Link>
                    <div
                      onClick={handleSignOut}
                      className="font-normal cursor-pointer flex items-center gap-2 text-primary py-2 px-2"
                    >
                      <div className="size-5 relative">
                        <Image src={"/log-out-02.png"} alt="" sizes="auto" fill quality={100} />
                      </div>
                      Log out
                    </div>
                      </div>
                    ) : (
                      <Link href={AUTH_ROUTES.SIGN_IN}>
                        <Button className="w-full">Login</Button>
                      </Link>
                    )}
                  </div>
                </SheetContent>
              </Sheet>
            </>
          ) : (
            <Link href={AUTH_ROUTES.SIGN_IN}>
              <Button className="w-28 h-11 lg:hidden">Login</Button>
            </Link>
          )}
        </div>
      </div>
    </div>
  );
};

export default Header;
