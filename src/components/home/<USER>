"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import clsx from "clsx";
import { useEffect, useState, useMemo } from "react";
import { Skeleton } from "@/components/ui/skeleton";
import DAppCard, { DApp } from "@/components/home/<USER>";
import { Swiper, SwiperProps, SwiperSlide } from "swiper/react";
import "swiper/css";
import Link from "next/link";
import { supabaseClient } from "@/lib/supabase/client";
import { Separator } from "../ui/separator";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { CATEGORIES } from "@/constants";

// Keep UI labels as in Figma design - Added "All" tab
const filterOptions = ["All", "DeFi", "NFT", "Games", "Tools", "Social", "Multi-chain"];

const swiperConfig: SwiperProps = {
  effect: "slide",
  slidesPerView: 1.1,
  centeredSlides: true,
  spaceBetween: 10,
  loop: true,
};

const PopularDapps = () => {
  const [active, setActive] = useState<string>("All");
  const [allDapps, setAllDapps] = useState<DApp[]>([]); // Store all DApps
  const [loading, setLoading] = useState<boolean>(true);
  const [currentPage, setCurrentPage] = useState<number>(1);

  // Filter logic (client-side)
  const filteredDapps = useMemo(() => {
    let filtered = allDapps;

    // Filter by category
    if (active !== "All") {
      const categoryMap: { [key: string]: string } = {
        "Games": "Games",
        "DeFi": "DeFi",
        "NFT": "NFT",
        "Tools": "Tools",
        "Social": "Social",
        "Multi-chain": "Multi-chain"
      };

      const dbCategory = categoryMap[active] || active;
      filtered = filtered.filter((dapp) => dapp.category === dbCategory);
    }

    return filtered;
  }, [allDapps, active]);

  // Pagination logic - Reduced for testing
  const ITEMS_PER_PAGE = active === "All" ? 4 : 2;
  const totalPages = Math.ceil(filteredDapps.length / ITEMS_PER_PAGE);
  const paginatedDapps = useMemo(() => {
    const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
    const endIndex = startIndex + ITEMS_PER_PAGE;
    return filteredDapps.slice(startIndex, endIndex);
  }, [filteredDapps, currentPage, ITEMS_PER_PAGE]);

  // Debug logs
  console.log('PopularDapps Debug:', {
    active,
    allDappsCount: allDapps.length,
    filteredDappsCount: filteredDapps.length,
    ITEMS_PER_PAGE,
    totalPages,
    currentPage,
    paginatedDappsCount: paginatedDapps.length
  });

  useEffect(() => {
    let isMounted = true;

    const fetchAllDapps = async () => {
      try {
        setLoading(true);

        // Fetch all DApps at once
        const { data } = await supabaseClient.from("dapps").select("*").limit(100).overrideTypes<DApp[]>();

        if (isMounted) {
          setAllDapps(data || []);
        }
      } catch (error) {
        if (isMounted) {
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    fetchAllDapps();

    return () => {
      isMounted = false;
    };
  }, []);

  // Reset to page 1 when category changes
  useEffect(() => {
    setCurrentPage(1);
  }, [active]);

  return (
    <div className="container lg:my-24 my-4">
      <div className="flex flex-col gap-2">
        <div className="flex justify-between items-center lg:items-start">
          <h2 className="uppercase font-protest-strike text-xl lg:text-3xl text-primary">
            Our popular Dapps
          </h2>
        </div>
        <p className="text-secondary-foreground block max-w-3xl text-sm lg:text-base">
          Real-world impact, seamless performance — these DApps are setting new standards in usability, scalability, and
          innovation across the decentralized ecosystem.
        </p>
      </div>

      <div className="flex mt-5 overflow-x-scroll gap-2 md:gap-4">
        {filterOptions.map((option) => (
          <Button
            onClick={() => setActive(option)}
            key={option}
            variant={"outline"}
            className={clsx(
              "h-10 min-w-[105px] text-secondary-foreground/80 px-4 rounded-xl",
              active === option &&
                "hover:bg-primary hover:text-white bg-primary text-white dark:!bg-white dark:text-background dark:hover:text-background font-semibold"
            )}
          >
            {option}
          </Button>
        ))}
      </div>

      <div className="mt-10 grid-cols-1 lg:grid-cols-4 gap-3 md:gap-5 hidden md:grid">
        {!loading ? (
          paginatedDapps.length > 0 ? (
            paginatedDapps.map((dapp) => {
              return <DAppCard key={dapp.id} {...dapp} />;
            })
          ) : (
            // Empty state
            <div className="col-span-4 flex flex-col items-center justify-center py-16 text-center">
              <div className="mb-4 text-6xl opacity-20">🔍</div>
              <h3 className="text-xl font-semibold mb-2">
                No {active} DApps Found
              </h3>
              <p className="text-secondary-foreground mb-4">
                We couldn't find any DApps in the {active} category yet.
              </p>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => setActive("All")}
                  className="mt-2"
                >
                  Browse All DApps
                </Button>
              </div>
            </div>
          )
        ) : (
          Array.from({ length: active === "All" ? 12 : 8 }).map((_, index) => (
            <div key={index} className="border p-5 rounded-2xl bg-card flex flex-col gap-3">
              <div className="flex gap-3">
                <Skeleton className="size-16 min-w-16 min-h-16 rounded-xl relative overflow-hidden" />
                <div className="flex flex-col gap-2 w-full">
                  <Skeleton className="h-1/2 w-full rounded-full" />
                  <Skeleton className="h-1/2 w-full rounded-full" />
                </div>
              </div>

              <Separator className="my-1" />

              <div className="grid grid-cols-2 gap-14">
                <div className="flex flex-col gap-1">
                  <p className="text-secondary-foreground text-sm">Total views</p>
                  <Skeleton className="h-5 w-full rounded-md" />
                </div>
                <div className="flex flex-col gap-1">
                  <p className="text-secondary-foreground text-sm">Avg time spend</p>
                  <Skeleton className="h-5 w-full rounded-md" />
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      <div className="mt-10 md:hidden">
        {!loading ? (
          paginatedDapps.length > 0 ? (
            <Swiper {...swiperConfig}>
              {paginatedDapps.map((dapp) => {
                return (
                  <SwiperSlide key={dapp.id}>
                    <DAppCard {...dapp} />
                  </SwiperSlide>
                );
              })}
            </Swiper>
          ) : (
            // Mobile empty state
            <div className="flex flex-col items-center justify-center py-16 text-center">
              <div className="mb-4 text-6xl opacity-20">🔍</div>
              <h3 className="text-xl font-semibold mb-2">
                No {active} DApps Found
              </h3>
              <p className="text-secondary-foreground mb-4 text-sm">
                We couldn't find any DApps in the {active} category yet.
              </p>
              <Button
                variant="outline"
                onClick={() => setActive("All")}
                className="mt-2"
                size="sm"
              >
                Browse All DApps
              </Button>
            </div>
          )
        ) : (
          // Mobile loading state
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="border p-5 rounded-2xl bg-card flex flex-col gap-3">
                <div className="flex gap-3">
                  <Skeleton className="size-16 min-w-16 min-h-16 rounded-xl relative overflow-hidden" />
                  <div className="flex flex-col gap-2 w-full">
                    <Skeleton className="h-1/2 w-full rounded-full" />
                    <Skeleton className="h-1/2 w-full rounded-full" />
                  </div>
                </div>
                <Separator className="my-1" />
                <div className="grid grid-cols-2 gap-14">
                  <div className="flex flex-col gap-1">
                    <p className="text-secondary-foreground text-sm">Total views</p>
                    <Skeleton className="h-5 w-full rounded-md" />
                  </div>
                  <div className="flex flex-col gap-1">
                    <p className="text-secondary-foreground text-sm">Avg time spend</p>
                    <Skeleton className="h-5 w-full rounded-md" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Debug info */}
      {!loading && (
        <div className="mt-4 p-2 bg-yellow-100 dark:bg-yellow-900 rounded text-xs">
          Debug: totalPages={totalPages}, filteredDapps.length={filteredDapps.length}, ITEMS_PER_PAGE={ITEMS_PER_PAGE}, active={active}
        </div>
      )}

      {/* Pagination - Show if we have data and more than 1 page OR for testing */}
      {!loading && (totalPages > 1 || filteredDapps.length > ITEMS_PER_PAGE) && (
        <div className="flex items-center justify-between mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            Showing {((currentPage - 1) * ITEMS_PER_PAGE) + 1} to {Math.min(currentPage * ITEMS_PER_PAGE, filteredDapps.length)} of {filteredDapps.length} dApps
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage((prev) => Math.max(1, prev - 1))}
              disabled={currentPage === 1 || loading}
              className="flex items-center gap-1"
            >
              <ChevronLeft className="w-4 h-4" />
              Previous
            </Button>

            <div className="flex items-center gap-1">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                let pageNum;
                if (totalPages <= 5) {
                  pageNum = i + 1;
                } else if (currentPage <= 3) {
                  pageNum = i + 1;
                } else if (currentPage >= totalPages - 2) {
                  pageNum = totalPages - 4 + i;
                } else {
                  pageNum = currentPage - 2 + i;
                }

                return (
                  <Button
                    key={pageNum}
                    variant={currentPage === pageNum ? "default" : "outline"}
                    size="sm"
                    onClick={() => setCurrentPage(pageNum)}
                    disabled={loading}
                    className="w-8 h-8 p-0"
                  >
                    {pageNum}
                  </Button>
                );
              })}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage((prev) => Math.min(totalPages, prev + 1))}
              disabled={currentPage === totalPages || loading}
              className="flex items-center gap-1"
            >
              Next
              <ChevronRight className="w-4 h-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default PopularDapps;
