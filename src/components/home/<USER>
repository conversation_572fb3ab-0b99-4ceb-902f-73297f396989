import slugify from "slugify";
import { Badge } from "@/components/ui/badge";
import Image from "next/image";
import React from "react";
import { Separator } from "@/components/ui/separator";
import Link from "next/link";
import { getSafeImageSrc } from "@/lib/utils/image";
import { openDapp } from "@/lib/utils";

export interface DApp {
  id: string;
  logo: string;
  name: string;
  category: string;
  description: string;
  live_url: string;
  avg_time_spend: number;
  total_views: number;
  created_at: string;
  user_id: string;
}

const DAppCard: React.FC<DApp> = ({
  category,
  id,
  logo,
  name,
  avg_time_spend,
  total_views,
}) => {
  return (
    <div
      onClick={() =>
        openDapp({ category, id, logo, name, avg_time_spend, total_views })
      }
      className="border p-5 rounded-2xl bg-card flex flex-col gap-3 cursor-pointer hover:shadow-lg transition-shadow duration-200 ease-in-out"
    >
      <div className="flex gap-3">
        <div className="size-16 min-w-16 min-h-16 rounded-xl relative overflow-hidden bg-gray-100 dark:bg-gray-700">
          {getSafeImageSrc(logo) ? (
            <Image
              src={getSafeImageSrc(logo)!}
              alt={name || "DApp logo"}
              sizes="auto"
              fill
              className="object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center text-gray-400 dark:text-gray-500 text-xs font-medium">
              No Logo
            </div>
          )}
        </div>
        <div className="flex flex-col gap-2">
          <p className="text-xl text-primary line-clamp-1 font-bold">{name}</p>
          <Badge className="bg-[#6745C14D] rounded-xl text-secondary-foreground px-3 text-xs py-2">
            {category}
          </Badge>
        </div>
      </div>

      <Separator />

      <div className="grid grid-cols-2">
        <div className="flex flex-col gap-1">
          <p className="text-secondary-foreground text-sm">Total views</p>
          <p className="font-medium text-primary">{total_views}</p>
        </div>
        <div className="flex flex-col gap-1">
          <p className="text-secondary-foreground text-sm">Avg time spend</p>
          <p className="font-medium text-primary">{avg_time_spend}</p>
        </div>
      </div>
    </div>
  );
};

export default DAppCard;
