"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import Image from "next/image";
import { motion } from "motion/react";
import { Swiper, SwiperProps, SwiperSlide } from "swiper/react";
import { useRouter } from "next/navigation";
import "swiper/css";

const swiperConfig: SwiperProps = {
  effect: "slide",
  slidesPerView: 1.2,
  initialSlide: 1,
  centeredSlides: true,
  spaceBetween: 20,
  loop: false,
};

const Pricing = () => {
  const router = useRouter();

  const handleStartPlan = (planName: string) => {
    router.push('/upload');
  };

  return (
    <div className="container lg:my-24 my-10">
      <h2 className="text-center font-protest-strike text-xl lg:text-[28px] text-primary uppercase">
        Simple, Transparent Pricing
      </h2>
      <p className="text-center mx-auto mt-2 text-secondary-foreground text-sm lg:text-base">
        Pay once, get discovered forever. No hidden fees—just pure Web3 exposure.
      </p>

      {/* desktop */}
      <div className="hidden lg:flex mt-20 w-fit gap-5 mx-auto">
        <motion.div
          whileInView={{
            opacity: [0, 100],
            y: [70, 0],
            transition: {
              ease: "easeInOut",
              duration: 0.5,
              delay: 0.4,
            },
          }}
          viewport={{ once: true }}
          className="w-[300px] p-8 rounded-2xl border bg-card flex flex-col gap-[18px] text-sm text-secondary-foreground"
        >
          <p>Start</p>
          <p className="flex gap-2 items-end">
            <span className="text-primary font-protest-strike text-3xl">$49</span>
            <span>Per month</span>
          </p>
          <div className="flex flex-col gap-2">
            <Button
              className="h-10 text-green border-green font-semibold"
              variant={"outline"}
              onClick={() => handleStartPlan("Start")}
            >
              Start this plan
            </Button>
            <div className="flex items-center justify-center gap-1">
              <p>Billed annual as</p>
              <p className="text-primary font-semibold">$588</p>
            </div>
          </div>
          <Separator />
          <p>Integration to OneWave</p>
          <p>Direct access to 1 of the telecom partners.</p>
        </motion.div>

        <motion.div
          whileInView={{
            opacity: [0, 100],
            y: [70, 0],
            transition: {
              ease: "easeInOut",
              duration: 0.5,
              delay: 0.2,
            },
          }}
          viewport={{ once: true }}
          className="w-[300px] p-8 text-white rounded-2xl border bg-card text-sm border-[#2FC890] relative z-10"
          style={{
            background: "linear-gradient(180deg, #013D29 0%, #00BD77 100%)",
            boxShadow:
              "0px 68px 19px 0px rgba(26, 162, 101, 0.01), 0px 44px 17px 0px rgba(26, 162, 101, 0.07), 0px 25px 15px 0px rgba(26, 162, 101, 0.23), 0px 11px 11px 0px rgba(26, 162, 101, 0.38), 0px 3px 6px 0px rgba(26, 162, 101, 0.44)",
          }}
        >
          <div className="absolute size-full top-0 left-0 z-0">
            <Image src={"/Light_upload-2 (2).gif"} alt="" sizes="auto" fill className="object-cover opacity-5" />
          </div>

          <div className="flex flex-col gap-[18px] relative z-10">
            <p>Scale</p>
            <p className="flex gap-2 items-end">
              <span className="text-[Primary text] font-protest-strike text-3xl">$99</span>
              <span>Per month</span>
            </p>
            <div className="flex flex-col gap-2">
              <Button
                className="h-10 text-green border-green font-semibold bg-transparent"
                variant={"outline"}
                onClick={() => handleStartPlan("Scale")}
              >
                Start this plan
              </Button>
              <div className="flex items-center justify-center gap-1">
                <p>Billed annual as</p>
                <p className="font-semibold">$1188</p>
              </div>
            </div>
            <Separator />
            <p className="text-white">Integrati on to OneWave</p>
            <p className="text-white">Direct access to 1 of the telecom partners.</p>
          </div>
        </motion.div>

        <motion.div
          whileInView={{
            opacity: [0, 100],
            y: [70, 0],
            transition: {
              ease: "easeInOut",
              duration: 0.5,
              delay: 0.6,
            },
          }}
          viewport={{ once: true }}
          className="w-[300px] p-8 rounded-2xl border bg-card flex flex-col gap-[18px] text-sm text-secondary-foreground"
        >
          <p>Conquer</p>
          <p className="flex gap-2 items-end">
            <span className="text-primary font-protest-strike text-3xl">$199</span>
            <span>Per month</span>
          </p>
          <div className="flex flex-col gap-2">
            <Button
              className="h-10 text-green border-green font-semibold"
              variant={"outline"}
              onClick={() => handleStartPlan("Conquer")}
            >
              Start this plan
            </Button>
            <div className="flex items-center justify-center gap-1">
              <p>Billed annual as</p>
              <p className="text-primary font-semibold">$2388</p>
            </div>
          </div>
          <Separator />
          <p>Integration to OneWave</p>
          <p>Direct access to 1 of the telecom partners.</p>
        </motion.div>
      </div>

      {/* mobile */}
      <div className="lg:hidden mt-10">
        <Swiper {...swiperConfig}>
          <SwiperSlide>
            <div className="p-8 rounded-2xl border bg-card flex flex-col gap-[18px] text-sm text-secondary-foreground">
              <p>Start</p>
              <p className="flex gap-2 items-end">
                <span className="text-primary font-protest-strike text-3xl">$49</span>
                <span>Per month</span>
              </p>
              <div className="flex flex-col gap-2">
                <Button
                  className="h-10 text-green border-green font-semibold"
                  variant={"outline"}
                  onClick={() => handleStartPlan("Start")}
                >
                  Start this plan
                </Button>
                <div className="flex items-center justify-center gap-1">
                  <p>Billed annual as</p>
                  <p className="text-primary font-semibold">$588</p>
                </div>
              </div>
              <Separator />
              <p>Integration to OneWave</p>
              <p>Direct access to 1 of the telecom partners.</p>
            </div>
          </SwiperSlide>
          <SwiperSlide>
            <div
              className="p-8 text-white rounded-2xl border bg-card text-sm border-[#2FC890] relative z-10"
              style={{
                background: "linear-gradient(180deg, #013D29 0%, #00BD77 100%)",
                boxShadow:
                  "0px 68px 19px 0px rgba(26, 162, 101, 0.01), 0px 44px 17px 0px rgba(26, 162, 101, 0.07), 0px 25px 15px 0px rgba(26, 162, 101, 0.23), 0px 11px 11px 0px rgba(26, 162, 101, 0.38), 0px 3px 6px 0px rgba(26, 162, 101, 0.44)",
              }}
            >
              <div className="absolute size-full top-0 left-0 z-0">
                <Image src={"/Light_upload-2 (2).gif"} alt="" sizes="auto" fill className="object-cover opacity-5" />
              </div>

              <div className="flex flex-col gap-[18px] relative z-10">
                <p>Scale</p>
                <p className="flex gap-2 items-end">
                  <span className="text-[Primary text] font-protest-strike text-3xl">$99</span>
                  <span>Per month</span>
                </p>
                <div className="flex flex-col gap-2">
                  <Button
                    className="h-10 text-green border-green font-semibold bg-transparent"
                    variant={"outline"}
                    onClick={() => handleStartPlan("Scale")}
                  >
                    Start this plan
                  </Button>
                  <div className="flex items-center justify-center gap-1">
                    <p>Billed annual as</p>
                    <p className="font-semibold">$1188</p>
                  </div>
                </div>
                <Separator />
                <p className="text-white">Integrati on to OneWave</p>
                <p className="text-white">Direct access to 1 of the telecom partners.</p>
              </div>
            </div>
          </SwiperSlide>
          <SwiperSlide>
            <div className="p-8 rounded-2xl border bg-card flex flex-col gap-[18px] text-sm text-secondary-foreground">
              <p>Conquer</p>
              <p className="flex gap-2 items-end">
                <span className="text-primary font-protest-strike text-3xl">$199</span>
                <span>Per month</span>
              </p>
              <div className="flex flex-col gap-2">
                <Button
                  className="h-10 text-green border-green font-semibold"
                  variant={"outline"}
                  onClick={() => handleStartPlan("Conquer")}
                >
                  Start this plan
                </Button>
                <div className="flex items-center justify-center gap-1">
                  <p>Billed annual as</p>
                  <p className="text-primary font-semibold">$2388</p>
                </div>
              </div>
              <Separator />
              <p>Integration to OneWave</p>
              <p>Direct access to 1 of the telecom partners.</p>
            </div>
          </SwiperSlide>
        </Swiper>
      </div>
    </div>
  );
};

export default Pricing;
