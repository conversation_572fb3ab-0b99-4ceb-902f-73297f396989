"use client";
import React from "react";
import { Separator } from "@/components/ui/separator";
import { useTheme } from "next-themes";

const Feedbacks = () => {
  const { theme } = useTheme();
  const isDarkMode = theme === "dark";

  return (
    <div className="mx-auto max-w-[1360px] mt-10 grid lg:grid-cols-2 my-24">
      <div className="flex flex-col gap-12">
        <h2 className="uppercase font-protest-strike text-xl lg:text-3xl">
          Review & Feedback Panel
        </h2>

        <div className="flex flex-col lg:flex-row gap-14 lg:items-center">
          <div className="flex flex-col gap-[6px]">
            <p className="text-sm text-secondary-foreground">Average rating</p>
            <p className="text-[52px] font-bold">4.2</p>
            <div className="flex gap-2 items-center">
              {Array.from({ length: 4 }).map((_, index) => (
                <svg
                  key={index}
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <path
                    fill="#FBAB29"
                    fillRule="evenodd"
                    d="m13.059.716 2.824 6.467q.045.105.133.173a.4.4 0 0 0 .198.078l6.751.813c.222.025.433.117.606.265.174.148.303.344.372.567.07.222.075.461.018.687s-.176.43-.341.588l-5.005 4.808a.437.437 0 0 0-.127.408l1.347 6.969c.046.229.027.466-.053.685-.08.218-.22.407-.4.544s-.396.218-.62.23a1.1 1.1 0 0 1-.639-.159l-5.918-3.494a.39.39 0 0 0-.41 0l-5.92 3.494a1.12 1.12 0 0 1-1.256-.071 1.2 1.2 0 0 1-.4-.545c-.081-.218-.1-.455-.055-.684l1.348-6.969a.44.44 0 0 0-.128-.408L.381 10.353a1.23 1.23 0 0 1-.342-.587 1.27 1.27 0 0 1 .018-.688c.07-.222.198-.42.372-.567.173-.148.384-.24.607-.265l6.751-.813a.4.4 0 0 0 .199-.078.4.4 0 0 0 .132-.173L10.942.716a1.2 1.2 0 0 1 .428-.521 1.12 1.12 0 0 1 1.26 0 1.2 1.2 0 0 1 .429.521"
                    clipRule="evenodd"
                  ></path>
                </svg>
              ))}
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                fill="none"
                viewBox="0 0 24 24"
              >
                <path
                  fill={isDarkMode ? "white" : "#cbcbcb"}
                  fillRule="evenodd"
                  d="m13.059.716 2.824 6.467q.045.105.133.173a.4.4 0 0 0 .198.078l6.751.813c.222.025.433.117.606.265.174.148.303.344.372.567.07.222.075.461.018.687s-.176.43-.341.588l-5.005 4.808a.437.437 0 0 0-.127.408l1.347 6.969c.046.229.027.466-.053.685-.08.218-.22.407-.4.544s-.396.218-.62.23a1.1 1.1 0 0 1-.639-.159l-5.918-3.494a.39.39 0 0 0-.41 0l-5.92 3.494a1.12 1.12 0 0 1-1.256-.071 1.2 1.2 0 0 1-.4-.545c-.081-.218-.1-.455-.055-.684l1.348-6.969a.44.44 0 0 0-.128-.408L.381 10.353a1.23 1.23 0 0 1-.342-.587 1.27 1.27 0 0 1 .018-.688c.07-.222.198-.42.372-.567.173-.148.384-.24.607-.265l6.751-.813a.4.4 0 0 0 .199-.078.4.4 0 0 0 .132-.173L10.942.716a1.2 1.2 0 0 1 .428-.521 1.12 1.12 0 0 1 1.26 0 1.2 1.2 0 0 1 .429.521"
                  clipRule="evenodd"
                ></path>
              </svg>
            </div>
          </div>
          <Separator orientation="vertical" className="hidden lg:block" />
          <div className="flex flex-col gap-3">
            <div className="flex items-center gap-8">
              <div className="flex items-center gap-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="12"
                  height="12"
                  fill="none"
                  viewBox="0 0 12 12"
                >
                  <path
                    fill="#FBAB29"
                    fillRule="evenodd"
                    d="m6.53.358 1.412 3.233a.2.2 0 0 0 .066.087q.045.034.1.039l3.375.406a.57.57 0 0 1 .303.133.6.6 0 0 1 .186.283.63.63 0 0 1-.162.638L9.308 7.581a.218.218 0 0 0-.064.204l.674 3.484a.63.63 0 0 1-.027.343.6.6 0 0 1-.2.272.56.56 0 0 1-.63.036l-2.958-1.747a.2.2 0 0 0-.205 0l-2.96 1.747a.56.56 0 0 1-.629-.036.6.6 0 0 1-.2-.272.63.63 0 0 1-.027-.343l.674-3.484a.22.22 0 0 0-.064-.204L.19 5.177a.634.634 0 0 1-.162-.638c.035-.111.1-.21.186-.284a.57.57 0 0 1 .304-.132l3.376-.406a.2.2 0 0 0 .099-.04.2.2 0 0 0 .066-.086L5.471.358a.6.6 0 0 1 .214-.26.56.56 0 0 1 .63 0 .6.6 0 0 1 .214.26"
                    clipRule="evenodd"
                  ></path>
                </svg>
                <p className="text-sm text-secondary-foreground">5</p>
              </div>

              <div className="w-[216px] rounded-full h-2 bg-primary/20 relative overflow-hidden">
                <div className="w-[65%] h-full bg-[#51AC54]"></div>
              </div>
            </div>
            <div className="flex items-center gap-8">
              <div className="flex items-center gap-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="12"
                  height="12"
                  fill="none"
                  viewBox="0 0 12 12"
                >
                  <path
                    fill="#FBAB29"
                    fillRule="evenodd"
                    d="m6.53.358 1.412 3.233a.2.2 0 0 0 .066.087q.045.034.1.039l3.375.406a.57.57 0 0 1 .303.133.6.6 0 0 1 .186.283.63.63 0 0 1-.162.638L9.308 7.581a.218.218 0 0 0-.064.204l.674 3.484a.63.63 0 0 1-.027.343.6.6 0 0 1-.2.272.56.56 0 0 1-.63.036l-2.958-1.747a.2.2 0 0 0-.205 0l-2.96 1.747a.56.56 0 0 1-.629-.036.6.6 0 0 1-.2-.272.63.63 0 0 1-.027-.343l.674-3.484a.22.22 0 0 0-.064-.204L.19 5.177a.634.634 0 0 1-.162-.638c.035-.111.1-.21.186-.284a.57.57 0 0 1 .304-.132l3.376-.406a.2.2 0 0 0 .099-.04.2.2 0 0 0 .066-.086L5.471.358a.6.6 0 0 1 .214-.26.56.56 0 0 1 .63 0 .6.6 0 0 1 .214.26"
                    clipRule="evenodd"
                  ></path>
                </svg>
                <p className="text-sm text-secondary-foreground">4</p>
              </div>

              <div className="w-[216px] rounded-full h-2 bg-primary/20 relative overflow-hidden">
                <div className="w-[45%] h-full bg-[#A6D032]"></div>
              </div>
            </div>
            <div className="flex items-center gap-8">
              <div className="flex items-center gap-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="12"
                  height="12"
                  fill="none"
                  viewBox="0 0 12 12"
                >
                  <path
                    fill="#FBAB29"
                    fillRule="evenodd"
                    d="m6.53.358 1.412 3.233a.2.2 0 0 0 .066.087q.045.034.1.039l3.375.406a.57.57 0 0 1 .303.133.6.6 0 0 1 .186.283.63.63 0 0 1-.162.638L9.308 7.581a.218.218 0 0 0-.064.204l.674 3.484a.63.63 0 0 1-.027.343.6.6 0 0 1-.2.272.56.56 0 0 1-.63.036l-2.958-1.747a.2.2 0 0 0-.205 0l-2.96 1.747a.56.56 0 0 1-.629-.036.6.6 0 0 1-.2-.272.63.63 0 0 1-.027-.343l.674-3.484a.22.22 0 0 0-.064-.204L.19 5.177a.634.634 0 0 1-.162-.638c.035-.111.1-.21.186-.284a.57.57 0 0 1 .304-.132l3.376-.406a.2.2 0 0 0 .099-.04.2.2 0 0 0 .066-.086L5.471.358a.6.6 0 0 1 .214-.26.56.56 0 0 1 .63 0 .6.6 0 0 1 .214.26"
                    clipRule="evenodd"
                  ></path>
                </svg>
                <p className="text-sm text-secondary-foreground">3</p>
              </div>

              <div className="w-[216px] rounded-full h-2 bg-primary/20 relative overflow-hidden">
                <div className="w-[63%] h-full bg-[#F7EB44]"></div>
              </div>
            </div>
            <div className="flex items-center gap-8">
              <div className="flex items-center gap-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="12"
                  height="12"
                  fill="none"
                  viewBox="0 0 12 12"
                >
                  <path
                    fill="#FBAB29"
                    fillRule="evenodd"
                    d="m6.53.358 1.412 3.233a.2.2 0 0 0 .066.087q.045.034.1.039l3.375.406a.57.57 0 0 1 .303.133.6.6 0 0 1 .186.283.63.63 0 0 1-.162.638L9.308 7.581a.218.218 0 0 0-.064.204l.674 3.484a.63.63 0 0 1-.027.343.6.6 0 0 1-.2.272.56.56 0 0 1-.63.036l-2.958-1.747a.2.2 0 0 0-.205 0l-2.96 1.747a.56.56 0 0 1-.629-.036.6.6 0 0 1-.2-.272.63.63 0 0 1-.027-.343l.674-3.484a.22.22 0 0 0-.064-.204L.19 5.177a.634.634 0 0 1-.162-.638c.035-.111.1-.21.186-.284a.57.57 0 0 1 .304-.132l3.376-.406a.2.2 0 0 0 .099-.04.2.2 0 0 0 .066-.086L5.471.358a.6.6 0 0 1 .214-.26.56.56 0 0 1 .63 0 .6.6 0 0 1 .214.26"
                    clipRule="evenodd"
                  ></path>
                </svg>
                <p className="text-sm text-secondary-foreground">2</p>
              </div>

              <div className="w-[216px] rounded-full h-2 bg-primary/20 relative overflow-hidden">
                <div className="w-[30%] h-full bg-[#FBA82C]"></div>
              </div>
            </div>
            <div className="flex items-center gap-8">
              <div className="flex items-center gap-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="12"
                  height="12"
                  fill="none"
                  viewBox="0 0 12 12"
                >
                  <path
                    fill="#FBAB29"
                    fillRule="evenodd"
                    d="m6.53.358 1.412 3.233a.2.2 0 0 0 .066.087q.045.034.1.039l3.375.406a.57.57 0 0 1 .303.133.6.6 0 0 1 .186.283.63.63 0 0 1-.162.638L9.308 7.581a.218.218 0 0 0-.064.204l.674 3.484a.63.63 0 0 1-.027.343.6.6 0 0 1-.2.272.56.56 0 0 1-.63.036l-2.958-1.747a.2.2 0 0 0-.205 0l-2.96 1.747a.56.56 0 0 1-.629-.036.6.6 0 0 1-.2-.272.63.63 0 0 1-.027-.343l.674-3.484a.22.22 0 0 0-.064-.204L.19 5.177a.634.634 0 0 1-.162-.638c.035-.111.1-.21.186-.284a.57.57 0 0 1 .304-.132l3.376-.406a.2.2 0 0 0 .099-.04.2.2 0 0 0 .066-.086L5.471.358a.6.6 0 0 1 .214-.26.56.56 0 0 1 .63 0 .6.6 0 0 1 .214.26"
                    clipRule="evenodd"
                  ></path>
                </svg>
                <p className="text-sm text-secondary-foreground">1</p>
              </div>

              <div className="w-[216px] rounded-full h-2 bg-primary/20 relative overflow-hidden">
                <div className="w-[30%] h-full bg-[#F03E1D]"></div>
              </div>
            </div>
          </div>
        </div>

        <div className="flex flex-col gap-4">
          <div className="flex gap-3 items-center">
            <div className="size-10 min-w-10 min-h-10 rounded-md bg-primary/10 flex items-center justify-center text-[22px] text-secondary-foreground/70">
              A
            </div>
            <div>
              <p className="text-sm text-primary">Akshay Ratnaparkhe</p>
              <div className="flex gap-1">
                {Array.from({ length: 4 }).map((_, index) => (
                  <svg
                    key={index}
                    xmlns="http://www.w3.org/2000/svg"
                    width="12"
                    height="12"
                    fill="none"
                    viewBox="0 0 12 12"
                  >
                    <path
                      fill="#FBAB29"
                      fillRule="evenodd"
                      d="m6.53.358 1.412 3.233a.2.2 0 0 0 .066.087q.045.034.1.039l3.375.406a.57.57 0 0 1 .303.133.6.6 0 0 1 .186.283.63.63 0 0 1-.162.638L9.308 7.581a.218.218 0 0 0-.064.204l.674 3.484a.63.63 0 0 1-.027.343.6.6 0 0 1-.2.272.56.56 0 0 1-.63.036l-2.958-1.747a.2.2 0 0 0-.205 0l-2.96 1.747a.56.56 0 0 1-.629-.036.6.6 0 0 1-.2-.272.63.63 0 0 1-.027-.343l.674-3.484a.22.22 0 0 0-.064-.204L.19 5.177a.634.634 0 0 1-.162-.638c.035-.111.1-.21.186-.284a.57.57 0 0 1 .304-.132l3.376-.406a.2.2 0 0 0 .099-.04.2.2 0 0 0 .066-.086L5.471.358a.6.6 0 0 1 .214-.26.56.56 0 0 1 .63 0 .6.6 0 0 1 .214.26"
                      clipRule="evenodd"
                    ></path>
                  </svg>
                ))}
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="12"
                  height="12"
                  fill="none"
                  viewBox="0 0 12 12"
                >
                  <path
                    fill={isDarkMode ? "white" : "#cbcbcb"}
                    fillRule="evenodd"
                    d="m6.53.358 1.412 3.233a.2.2 0 0 0 .066.087q.045.034.1.039l3.375.406a.57.57 0 0 1 .303.133.6.6 0 0 1 .186.283.63.63 0 0 1-.162.638L9.308 7.581a.218.218 0 0 0-.064.204l.674 3.484a.63.63 0 0 1-.027.343.6.6 0 0 1-.2.272.56.56 0 0 1-.63.036l-2.958-1.747a.2.2 0 0 0-.205 0l-2.96 1.747a.56.56 0 0 1-.629-.036.6.6 0 0 1-.2-.272.63.63 0 0 1-.027-.343l.674-3.484a.22.22 0 0 0-.064-.204L.19 5.177a.634.634 0 0 1-.162-.638c.035-.111.1-.21.186-.284a.57.57 0 0 1 .304-.132l3.376-.406a.2.2 0 0 0 .099-.04.2.2 0 0 0 .066-.086L5.471.358a.6.6 0 0 1 .214-.26.56.56 0 0 1 .63 0 .6.6 0 0 1 .214.26"
                    clipRule="evenodd"
                  ></path>
                </svg>
              </div>
            </div>
          </div>
          <p className="text-sm text-secondary-foreground">
            The dapp is just wonderful, amazing. It is super addictive and I am
            playing it from so many years. Recently, I have seen so many
            disconnections; all the progress gets lost. I have tried switching
            networks but it did not help. After playing half an hour or more and
            if the progress gets lost, it is annoying to start from the same
            point from past. The dapp is already of slow pace but this kind of
            issues add more delays.
          </p>
        </div>

        <div className="flex flex-col gap-4">
          <div className="flex gap-3 items-center">
            <div className="size-10 min-w-10 min-h-10 rounded-md bg-primary/10 flex items-center justify-center text-[22px] text-secondary-foreground/70">
              A
            </div>
            <div>
              <p className="text-sm text-primary">Akshay Ratnaparkhe</p>
              <div className="flex gap-1">
                {Array.from({ length: 4 }).map((_, index) => (
                  <svg
                    key={index}
                    xmlns="http://www.w3.org/2000/svg"
                    width="12"
                    height="12"
                    fill="none"
                    viewBox="0 0 12 12"
                  >
                    <path
                      fill="#FBAB29"
                      fillRule="evenodd"
                      d="m6.53.358 1.412 3.233a.2.2 0 0 0 .066.087q.045.034.1.039l3.375.406a.57.57 0 0 1 .303.133.6.6 0 0 1 .186.283.63.63 0 0 1-.162.638L9.308 7.581a.218.218 0 0 0-.064.204l.674 3.484a.63.63 0 0 1-.027.343.6.6 0 0 1-.2.272.56.56 0 0 1-.63.036l-2.958-1.747a.2.2 0 0 0-.205 0l-2.96 1.747a.56.56 0 0 1-.629-.036.6.6 0 0 1-.2-.272.63.63 0 0 1-.027-.343l.674-3.484a.22.22 0 0 0-.064-.204L.19 5.177a.634.634 0 0 1-.162-.638c.035-.111.1-.21.186-.284a.57.57 0 0 1 .304-.132l3.376-.406a.2.2 0 0 0 .099-.04.2.2 0 0 0 .066-.086L5.471.358a.6.6 0 0 1 .214-.26.56.56 0 0 1 .63 0 .6.6 0 0 1 .214.26"
                      clipRule="evenodd"
                    ></path>
                  </svg>
                ))}
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="12"
                  height="12"
                  fill="none"
                  viewBox="0 0 12 12"
                >
                  <path
                    fill={isDarkMode ? "white" : "#cbcbcb"}
                    fillRule="evenodd"
                    d="m6.53.358 1.412 3.233a.2.2 0 0 0 .066.087q.045.034.1.039l3.375.406a.57.57 0 0 1 .303.133.6.6 0 0 1 .186.283.63.63 0 0 1-.162.638L9.308 7.581a.218.218 0 0 0-.064.204l.674 3.484a.63.63 0 0 1-.027.343.6.6 0 0 1-.2.272.56.56 0 0 1-.63.036l-2.958-1.747a.2.2 0 0 0-.205 0l-2.96 1.747a.56.56 0 0 1-.629-.036.6.6 0 0 1-.2-.272.63.63 0 0 1-.027-.343l.674-3.484a.22.22 0 0 0-.064-.204L.19 5.177a.634.634 0 0 1-.162-.638c.035-.111.1-.21.186-.284a.57.57 0 0 1 .304-.132l3.376-.406a.2.2 0 0 0 .099-.04.2.2 0 0 0 .066-.086L5.471.358a.6.6 0 0 1 .214-.26.56.56 0 0 1 .63 0 .6.6 0 0 1 .214.26"
                    clipRule="evenodd"
                  ></path>
                </svg>
              </div>
            </div>
          </div>
          <p className="text-sm text-secondary-foreground">
            The dapp is just wonderful, amazing. It is super addictive and I am
            playing it from so many years. Recently, I have seen so many
            disconnections; all the progress gets lost. I have tried switching
            networks but it did not help. After playing half an hour or more and
            if the progress gets lost, it is annoying to start from the same
            point from past. The dapp is already of slow pace but this kind of
            issues add more delays.
          </p>
        </div>
      </div>
    </div>
  );
};

export default Feedbacks;
