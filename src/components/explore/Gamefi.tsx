"use client";
import React, { useEffect, useState } from "react";
import DAppCard, { DApp } from "@/components/home/<USER>";
import { Swiper, SwiperProps, SwiperSlide } from "swiper/react";
import "swiper/css";
import { supabaseClient } from "@/lib/supabase/client";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";

const swiperConfig: SwiperProps = {
  effect: "slide",
  slidesPerView: 1.1,
  centeredSlides: true,
  spaceBetween: 10,
  loop: true,
};

// Pagination constants
const ITEMS_PER_PAGE = 8;

const GameFi = () => {
  const [loading, setLoading] = useState<boolean>(true);
  const [dapps, setDapps] = useState<DApp[]>([]);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [totalItems, setTotalItems] = useState<number>(0);

  useEffect(() => {
    const fetchDapps = async () => {
      try {
        setLoading(true);

        // Use direct Supabase call like PopularDapps
        const { data, error } = await supabaseClient
          .from("dapps")
          .select("*")
          .eq("category", "Games")
          .limit(100) // Get more data for pagination
          .overrideTypes<DApp[]>();

        console.log('GameFi Supabase Response:', { data, error });

        if (data) {
          // Client-side pagination like PopularDapps
          const totalItems = data.length;
          const totalPages = Math.ceil(totalItems / ITEMS_PER_PAGE);
          const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
          const endIndex = startIndex + ITEMS_PER_PAGE;
          const paginatedData = data.slice(startIndex, endIndex);

          setDapps(paginatedData);
          setTotalPages(totalPages);
          setTotalItems(totalItems);
        }
      } catch (error) {
        console.error('GameFi fetch error:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDapps();
  }, [currentPage]);

  return (
    <div className="container my-10 lg:my-[118px]">
      <p className="font-protest-strike text-2xl lg:text-3xl uppercase">gamefi</p>

      <div className="mt-10 grid-col-1 lg:grid-cols-4 gap-3 md:gap-5 hidden md:grid">
        {!loading ? (
          dapps.length > 0 ? (
            dapps.map((dapp: DApp) => {
              return <DAppCard key={dapp.id} {...dapp} />;
            })
          ) : (
            <div className="col-span-4 flex flex-col items-center justify-center py-16 text-center">
              <div className="mb-4 text-6xl opacity-20">🎮</div>
              <h3 className="text-xl font-semibold mb-2">No GameFi DApps Found</h3>
              <p className="text-secondary-foreground">
                We couldn't find any DApps in the GameFi category yet.
              </p>
            </div>
          )
        ) : (
          // Loading skeletons
          Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className="border p-5 rounded-2xl bg-card flex flex-col gap-3">
              <div className="flex gap-3">
                <div className="size-16 min-w-16 min-h-16 rounded-xl bg-gray-200 dark:bg-gray-700 animate-pulse" />
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-3/4 animate-pulse" />
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      <div className="mt-4 md:hidden">
        {!loading ? (
          dapps.length > 0 ? (
            <Swiper {...swiperConfig}>
              {dapps.map((dapp: DApp) => {
                return (
                  <SwiperSlide key={dapp.id}>
                    <DAppCard {...dapp} />
                  </SwiperSlide>
                );
              })}
            </Swiper>
          ) : (
            <div className="flex flex-col items-center justify-center py-16 text-center">
              <div className="mb-4 text-6xl opacity-20">🎮</div>
              <h3 className="text-xl font-semibold mb-2">No GameFi DApps Found</h3>
              <p className="text-secondary-foreground">
                We couldn't find any DApps in the GameFi category yet.
              </p>
            </div>
          )
        ) : (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        )}
      </div>

      {/* Pagination */}
      {!loading && totalPages > 1 && (
        <div className="flex flex-col sm:flex-row items-center justify-between mt-8 pt-6 border-t border-gray-200 dark:border-gray-700 gap-4">
          <div className="text-sm text-gray-600 dark:text-gray-400 order-2 sm:order-1">
            Showing {((currentPage - 1) * ITEMS_PER_PAGE) + 1} to {Math.min(currentPage * ITEMS_PER_PAGE, totalItems)} of {totalItems} GameFi dApps
          </div>

          <div className="flex items-center gap-2 order-1 sm:order-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage((prev) => Math.max(1, prev - 1))}
              disabled={currentPage === 1 || loading}
              className="flex items-center gap-1"
            >
              <ChevronLeft className="w-4 h-4" />
              <span className="hidden sm:inline">Previous</span>
            </Button>

            <div className="flex items-center gap-1">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                let pageNum;
                if (totalPages <= 5) {
                  pageNum = i + 1;
                } else if (currentPage <= 3) {
                  pageNum = i + 1;
                } else if (currentPage >= totalPages - 2) {
                  pageNum = totalPages - 4 + i;
                } else {
                  pageNum = currentPage - 2 + i;
                }

                return (
                  <Button
                    key={pageNum}
                    variant={currentPage === pageNum ? "default" : "outline"}
                    size="sm"
                    onClick={() => setCurrentPage(pageNum)}
                    disabled={loading}
                    className="w-8 h-8 p-0"
                  >
                    {pageNum}
                  </Button>
                );
              })}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage((prev) => Math.min(totalPages, prev + 1))}
              disabled={currentPage === totalPages || loading}
              className="flex items-center gap-1"
            >
              <span className="hidden sm:inline">Next</span>
              <ChevronRight className="w-4 h-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default GameFi;
