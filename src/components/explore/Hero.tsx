import React from "react";
import Header from "@/components/home/<USER>";
import Image from "next/image";

const Hero = () => {
  return (
    <div className="w-screen h-fit flex flex-col relative bg-background/40">
      <div className="absolute top-0 left-1/2 -translate-x-1/2 size-full z-0">
        <Image
          src={"/Polygon.svg"}
          alt=""
          sizes="auto"
          fill
          className="object-contain object-top dark:opacity-50 opacity-80"
        />
      </div>

      <div className="absolute top-16 left-1/2 -translate-x-1/2 h-[460px] w-full lg:w-[524px] z-10 opacity-20">
        <Image
          src={"/explore_bg.svg"}
          alt=""
          sizes="auto"
          fill
          className="object-contain object-top"
        />
      </div>

      <div className="absolute top-16 left-1/2 -translate-x-1/2 h-[460px] w-full lg:w-[524px] z-20 dark:bg-background/30 bg-background/20" />

      <Header />

      <div className="mt-28 lg:mt-64 flex items-center justify-center z-50 relative">
        <div className="w-fit mx-auto text-center flex flex-col gap-3">
          <h1 className="text-3xl lg:text-[52px] uppercase font-protest-strike">
            Explore the World of Next-gen DApps
          </h1>
          <p className="mx-auto text-sm lg:text-xl text-secondary-foreground">
            Browse a curated collection of powerful DApps spanning DeFi, NFTs,
            Gaming, <br className="hidden lg:block" />
            Productivity, and more — all designed for smooth interaction and
            practical utility.
          </p>
        </div>
      </div>
    </div>
  );
};

export default Hero;
