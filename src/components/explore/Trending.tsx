"use client";
import React, { useEffect } from "react";
import DAppCard, { DApp } from "@/components/home/<USER>";

import { Swiper, SwiperProps, SwiperSlide } from "swiper/react";
import "swiper/css";
import { supabaseClient } from "@/lib/supabase/client";

const swiperConfig: SwiperProps = {
  effect: "slide",
  slidesPerView: 1.1,
  centeredSlides: true,
  spaceBetween: 10,
  loop: true,
};

const Trending = () => {
  const [dapps, setDapps] = React.useState<DApp[]>([]);

  useEffect(() => {
    const fetchDapp = async () => {
      const { data } = await supabaseClient.from("dapps").select("*").limit(8);
      if (data) setDapps(data);
    };

    fetchDapp();
  }, []);

  return (
    <div className="container my-10 lg:my-[118px] relative z-50">
      <p className="mb-2 lg:mb-0 font-protest-strike text-2xl lg:text-3xl uppercase">Trending Now</p>

      <div className="hidden lg:grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5 my-10">
        {dapps.map((dapp) => {
          return <DAppCard key={dapp.id} {...dapp} />;
        })}
      </div>

      <div className="lg:hidden">
        <Swiper {...swiperConfig}>
          {dapps.map((dapp) => {
            return (
              <SwiperSlide key={dapp.id}>
                <DAppCard key={dapp.id} {...dapp} />
              </SwiperSlide>
            );
          })}
        </Swiper>
      </div>
    </div>
  );
};

export default Trending;
