"use client";
import React, { useEffect } from "react";
import DAppCard, { DApp } from "@/components/home/<USER>";

import { Swiper, SwiperProps, SwiperSlide } from "swiper/react";
import "swiper/css";
import { supabaseClient } from "@/lib/supabase/client";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { CategoryType } from "@/constants";

const swiperConfig: SwiperProps = {
  effect: "slide",
  slidesPerView: 1.1,
  centeredSlides: true,
  spaceBetween: 10,
  loop: true,
};

const Trending = () => {
  const [dapps, setDapps] = React.useState<DApp[]>([]);
  const router = useRouter();

  useEffect(() => {
    const fetchDapp = async () => {
      const { data } = await supabaseClient.from("dapps").select("*").limit(8);
      if (data) setDapps(data);
    };

    fetchDapp();
  }, []);

  return (
    <div className="container my-10 lg:my-[118px] relative z-50">
      <div className="flex items-center justify-between">
        <p className="mb-2 lg:mb-0 font-protest-strike text-2xl lg:text-3xl uppercase">Trending Now</p>
        <Button
          onClick={() => {
            router.push(`/explore/${CategoryType.Trending}`);
          }}
          variant="outline"
          className="px-6 py-2 text-primary border-primary hover:bg-primary hover:text-white transition-colors font-semibold whitespace-nowrap"
        >
          View All
        </Button>
      </div>

      <div className="hidden lg:grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5 my-10">
        {dapps.length > 0 ? (
          dapps.map((dapp) => {
            return <DAppCard key={dapp.id} {...dapp} />;
          })
        ) : (
          // Desktop empty state
          <div className="col-span-4 flex flex-col items-center justify-center py-16 text-center">
            <div className="mb-4 text-6xl opacity-20">📈</div>
            <h3 className="text-xl font-semibold mb-2">No Trending DApps Found</h3>
            <p className="text-secondary-foreground mb-4">We couldn't find any trending DApps at the moment.</p>
          </div>
        )}
      </div>

      <div className="lg:hidden">
        {dapps.length > 0 ? (
          <Swiper {...swiperConfig}>
            {dapps.map((dapp) => {
              return (
                <SwiperSlide key={dapp.id}>
                  <DAppCard key={dapp.id} {...dapp} />
                </SwiperSlide>
              );
            })}
          </Swiper>
        ) : (
          // Mobile empty state
          <div className="flex flex-col items-center justify-center py-16 text-center">
            <div className="mb-4 text-6xl opacity-20">📈</div>
            <h3 className="text-xl font-semibold mb-2">No Trending DApps Found</h3>
            <p className="text-secondary-foreground mb-4 text-sm">We couldn't find any trending DApps at the moment.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Trending;
