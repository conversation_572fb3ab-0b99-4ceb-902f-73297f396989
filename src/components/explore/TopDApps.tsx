"use client";

import * as React from "react";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import Image from "next/image";
import { Badge } from "@/components/ui/badge";
import { supabaseClient } from "@/lib/supabase/client";
import { DApp } from "../home/<USER>";
import { getSafeImageSrc } from "@/lib/utils/image";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";


export interface Row {
  rank: string;
  name: string;
  image: string;
  category: string;
  totalViews: number;
  avgTimeSpend: number;
}

export const columns: ColumnDef<Row>[] = [
  {
    accessorKey: "rank",
    header: "Rank",
    cell: ({ row }) => <div className="capitalize text-secondary-foreground">#{row.getValue("rank")}</div>,
  },
  {
    accessorKey: "name",
    header: ({ column }) => {
      return <div>Name</div>;
    },
    cell: ({ row }) => (
      <div className="flex gap-3 items-center">
        <div className="relative size-16 rounded-xl overflow-hidden">
          <Image src={row.original.image} alt="" sizes="auto" fill className="object-cover" />
        </div>
        <div className="font-semibold">{row.getValue("name")}</div>
      </div>
    ),
  },
  {
    accessorKey: "category",
    header: ({ column }) => {
      return <div>Category</div>;
    },
    cell: ({ row }) => (
      <Badge className="bg-[#6745C14D] rounded-xl text-secondary-foreground px-3 text-xs py-2">
        {row.getValue("category")}
      </Badge>
    ),
  },
  {
    accessorKey: "totalViews",
    header: ({ column }) => {
      return <div>Total Views</div>;
    },
    cell: ({ row }) => {
      const totalViews = row.getValue("totalViews") as string;
      return <div>{totalViews.toLocaleString()}</div>;
    },
  },
  {
    accessorKey: "avgTimeSpend",
    header: ({ column }) => {
      return <div>Avg time spend</div>;
    },
    cell: ({ row }) => <div>{row.getValue("avgTimeSpend")}</div>,
  },
];

interface TopDAppsProps {
  isExplorePage?: boolean;
}

function TopDApps({ isExplorePage = false }: TopDAppsProps) {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});
  const [data, setData] = React.useState<Row[]>([]);
  const router = useRouter();

  React.useEffect(() => {
    const fetchDapps = async () => {
      try {
        const { data } = await supabaseClient.from("dapps").select("*").order('total_views', { ascending: false }).limit(20).overrideTypes<DApp[]>();
        const formattedData =
          data?.map((dapp, index) => ({
            rank: (index + 1).toString(),
            name: dapp.name,
            image: getSafeImageSrc(dapp.logo) || "/dapps/image.svg",
            category: dapp.category,
            totalViews: dapp.total_views,
            avgTimeSpend: dapp.avg_time_spend,
          })) || [];
        setData(formattedData);
      } catch (error) {}
    };

    fetchDapps();
  }, []);

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  return (
    <div className="container lg:my-24 my-4 overflow-x-scroll">
      <div className="flex items-center justify-between w-full">
        <p className="font-protest-strike text-3xl uppercase">Top Dapps</p>
        <Button
          onClick={() => router.push(isExplorePage ? "/explore/top-dapps" : "/explore")}
          variant="outline"
          className="px-6 py-2 text-primary border-primary hover:bg-primary hover:text-white transition-colors font-semibold whitespace-nowrap"
        >
          View All
        </Button>
      </div>

      <div className="rounded-md mt-10 max-h-[400px] overflow-y-auto">
        <Table>
          <TableHeader className="sticky top-0 bg-white dark:bg-gray-900 z-10">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id} className="h-16 py-6">
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow className="h-16 py-6" key={row.id} data-state={row.getIsSelected() && "selected"}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-32 text-center">
                  <div className="flex flex-col items-center justify-center py-8">
                    <div className="mb-2 text-4xl opacity-20">📊</div>
                    <h3 className="text-lg font-semibold mb-1">No Top DApps Found</h3>
                    <p className="text-secondary-foreground text-sm">
                      We couldn't find any DApps to display in the rankings yet.
                    </p>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

export default TopDApps;
