"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import DAppCard, { DApp } from "@/components/home/<USER>";
import { Skeleton } from "@/components/ui/skeleton";
import { Separator } from "@/components/ui/separator";
import Link from "next/link";

const Web3Categories = () => {
  const [nftDapps, setNftDapps] = useState<DApp[]>([]);
  const [walletDapps, setWalletDapps] = useState<DApp[]>([]);
  const [securityDapps, setSecurityDapps] = useState<DApp[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const fetchCategoryDapps = async () => {
      try {
        setLoading(true);
        
        // Fetch NFT DApps
        const nftRes = await fetch(`/api/v1/dapps/explore?category=NFT&page=1&limit=3`);
        const nftData = await nftRes.json();
        
        // Fetch Wallet DApps
        const walletRes = await fetch(`/api/v1/dapps/explore?category=Wallet&page=1&limit=3`);
        const walletData = await walletRes.json();
        
        // Fetch Security DApps
        const securityRes = await fetch(`/api/v1/dapps/explore?category=Security&page=1&limit=3`);
        const securityData = await securityRes.json();

        if (nftData.data) {
          setNftDapps(nftData.data.dapps || []);
        }
        if (walletData.data) {
          setWalletDapps(walletData.data.dapps || []);
        }
        if (securityData.data) {
          setSecurityDapps(securityData.data.dapps || []);
        }
      } catch (error) {
        console.error("Error fetching category dapps:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchCategoryDapps();
  }, []);

  const CategorySection = ({ 
    title, 
    dapps, 
    categoryLink 
  }: { 
    title: string; 
    dapps: DApp[]; 
    categoryLink: string;
  }) => (
    <div className="bg-gray-50 dark:bg-gray-800/50 rounded-2xl p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-bold text-gray-900 dark:text-white uppercase">
          {title}
        </h3>
        <Link href={categoryLink}>
          <Button variant="ghost" size="sm" className="text-primary hover:text-primary/80">
            View All
          </Button>
        </Link>
      </div>
      
      <div className="space-y-4">
        {loading ? (
          Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className="flex gap-3 p-3 bg-white dark:bg-gray-900 rounded-xl">
              <Skeleton className="size-12 min-w-12 min-h-12 rounded-lg" />
              <div className="flex flex-col gap-2 w-full">
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-3 w-1/2" />
              </div>
              <div className="text-right">
                <Skeleton className="h-3 w-12 mb-1" />
                <Skeleton className="h-4 w-8" />
              </div>
            </div>
          ))
        ) : dapps.length > 0 ? (
          dapps.slice(0, 3).map((dapp) => (
            <div key={dapp.id} className="flex items-center gap-3 p-3 bg-white dark:bg-gray-900 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
              <img 
                src={dapp.logo} 
                alt={dapp.name}
                className="size-12 min-w-12 min-h-12 rounded-lg object-cover"
              />
              <div className="flex-1 min-w-0">
                <h4 className="font-semibold text-gray-900 dark:text-white truncate">
                  {dapp.name}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                  {dapp.description}
                </p>
              </div>
              <div className="text-right">
                <p className="text-xs text-gray-500 dark:text-gray-400">Total views</p>
                <p className="font-semibold text-gray-900 dark:text-white">
                  {dapp.total_views || 0}
                </p>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500 dark:text-gray-400">No {title.toLowerCase()} DApps found</p>
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className="container lg:my-24 my-4">
      <div className="flex flex-col gap-2 mb-8">
        <h2 className="uppercase font-protest-strike text-xl lg:text-3xl text-primary">
          WEB 3
        </h2>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <CategorySection 
          title="NFT" 
          dapps={nftDapps} 
          categoryLink="/explore/NFT"
        />
        <CategorySection 
          title="WALLET" 
          dapps={walletDapps} 
          categoryLink="/explore/Wallet"
        />
        <CategorySection 
          title="SECURITY" 
          dapps={securityDapps} 
          categoryLink="/explore/Security"
        />
      </div>
    </div>
  );
};

export default Web3Categories;
