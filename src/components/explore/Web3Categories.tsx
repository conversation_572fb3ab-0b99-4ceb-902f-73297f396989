"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import DAppCard, { DApp } from "@/components/home/<USER>";
import { Skeleton } from "@/components/ui/skeleton";
import { Separator } from "@/components/ui/separator";
import Link from "next/link";

const Web3Categories = () => {
  const [nftDapps, setNftDapps] = useState<DApp[]>([]);
  const [walletDapps, setWalletDapps] = useState<DApp[]>([]);
  const [securityDapps, setSecurityDapps] = useState<DApp[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const fetchCategoryDapps = async () => {
      try {
        setLoading(true);
        
        // Fetch NFT DApps
        const nftRes = await fetch(`/api/v1/dapps/explore?category=NFT&page=1&limit=3`);
        const nftData = await nftRes.json();
        
        // Fetch Wallet DApps
        const walletRes = await fetch(`/api/v1/dapps/explore?category=Wallet&page=1&limit=3`);
        const walletData = await walletRes.json();
        
        // Fetch Security DApps
        const securityRes = await fetch(`/api/v1/dapps/explore?category=Security&page=1&limit=3`);
        const securityData = await securityRes.json();

        if (nftData.data) {
          setNftDapps(nftData.data.dapps || []);
        }
        if (walletData.data) {
          setWalletDapps(walletData.data.dapps || []);
        }
        if (securityData.data) {
          setSecurityDapps(securityData.data.dapps || []);
        }
      } catch (error) {
        console.error("Error fetching category dapps:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchCategoryDapps();
  }, []);

  const CategorySection = ({
    title,
    dapps
  }: {
    title: string;
    dapps: DApp[];
  }) => (
    <div style={{ backgroundColor: '#F5F7F9' }} className="rounded-2xl p-6">
      <div className="mb-6">
        <h3 className="text-lg font-bold text-black uppercase">
          {title}
        </h3>
      </div>

      <div className="space-y-4">
        {loading ? (
          Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className="flex gap-3 p-3 bg-white rounded-xl">
              <Skeleton className="size-12 min-w-12 min-h-12 rounded-lg" />
              <div className="flex flex-col gap-2 w-full">
                <Skeleton className="h-4 w-3/4" />
              </div>
              <div className="text-right">
                <Skeleton className="h-3 w-12 mb-1" />
                <Skeleton className="h-4 w-8" />
              </div>
            </div>
          ))
        ) : dapps.length > 0 ? (
          dapps.slice(0, 3).map((dapp) => (
            <div key={dapp.id} className="flex items-center gap-3 p-3 bg-white rounded-xl">
              <img
                src={dapp.logo || '/placeholder-logo.png'}
                alt={dapp.name}
                className="size-12 min-w-12 min-h-12 rounded-lg object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = '/placeholder-logo.png';
                }}
              />
              <div className="flex-1 min-w-0">
                <h4 className="font-semibold text-black truncate">
                  {dapp.name}
                </h4>
              </div>
              <div className="text-right">
                <p className="text-xs text-gray-500">Total views</p>
                <p className="font-semibold text-black">
                  {dapp.total_views || 0}
                </p>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500">No {title.toLowerCase()} DApps found</p>
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className="container lg:my-24 my-4">
      <div className="flex flex-col gap-2 mb-8">
        <h2 className="uppercase font-protest-strike text-xl lg:text-3xl text-primary">
          WEB 3
        </h2>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <CategorySection
          title="NFT"
          dapps={nftDapps}
        />
        <CategorySection
          title="WALLET"
          dapps={walletDapps}
        />
        <CategorySection
          title="SECURITY"
          dapps={securityDapps}
        />
      </div>
    </div>
  );
};

export default Web3Categories;
