"use client";

import DAppCard, { DApp } from "@/components/home/<USER>";
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import clsx from "clsx";

import { Swiper, SwiperProps, SwiperSlide } from "swiper/react";
import "swiper/css";
import { supabaseClient } from "@/lib/supabase/client";
import { Skeleton } from "../ui/skeleton";
import { Separator } from "../ui/separator";

const swiperConfig: SwiperProps = {
  effect: "slide",
  slidesPerView: 1.1,
  centeredSlides: true,
  spaceBetween: 10,
  loop: true,
};

const filterOptions = ["DeFi", "NFT", "Games", "Tools", "Social", "Multi-chain"];

const DApps = () => {
  const [active, setActive] = useState<string>("DeFi");
  const [dapps, setDapps] = useState<DApp[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const fetchDapps = async () => {
      try {
        setLoading(true);
        const { data } = await supabaseClient
          .from("dapps")
          .select("*")
          .eq("category", active)
          .limit(20)
          .overrideTypes<DApp[]>();

        setDapps(data || []);
      } catch (error) {
      } finally {
        setLoading(false);
      }
    };

    fetchDapps();
  }, [active]);

  return (
    <div className="container lg:my-24 my-4">
      <div className="flex justify-between items-center">
        <p className="font-protest-strike text-3xl uppercase">Dapps</p>
      </div>

      <div className="flex mt-5 overflow-x-scroll gap-2 md:gap-4">
        {filterOptions.map((option) => (
          <Button
            onClick={() => setActive(option)}
            key={option}
            variant={"outline"}
            className={clsx(
              "h-10 min-w-[105px] text-secondary-foreground/80 px-4 rounded-xl",
              active === option &&
                "hover:bg-primary hover:text-white bg-primary text-white dark:!bg-white dark:text-background dark:hover:text-background font-semibold"
            )}
          >
            {option}
          </Button>
        ))}
      </div>

      <div className="mt-10 grid-col-1 lg:grid-cols-4 gap-3 md:gap-5 hidden md:grid">
        {!loading ? (
          dapps.filter((dapp) => active === dapp.category).length > 0 ? (
            dapps
              .filter((dapp) => active === dapp.category)
              .map((dapp) => {
                return <DAppCard key={dapp.id} {...dapp} />;
              })
          ) : (
            <div className="col-span-4 flex flex-col items-center justify-center py-16 text-center">
              <div className="mb-4 text-6xl opacity-20">
                {active === 'DeFi' ? '💰' :
                 active === 'NFT' ? '🖼️' :
                 active === 'Games' ? '🎮' :
                 active === 'Tools' ? '🔧' :
                 active === 'Social' ? '👥' :
                 active === 'Multi-chain' ? '🔗' : '📱'}
              </div>
              <h3 className="text-xl font-semibold mb-2">No {active} DApps Found</h3>
              <p className="text-secondary-foreground">
                We couldn't find any DApps in the {active} category yet.
              </p>
            </div>
          )
        ) : (
          Array.from({ length: 8 }).map((_, index) => (
            <div key={index} className="border p-5 rounded-2xl bg-card flex flex-col gap-3">
              <div className="flex gap-3">
                <Skeleton className="size-16 min-w-16 min-h-16 rounded-xl relative overflow-hidden" />
                <div className="flex flex-col gap-2 w-full">
                  <Skeleton className="h-1/2 w-full rounded-full" />
                  <Skeleton className="h-1/2 w-full rounded-full" />
                </div>
              </div>

              <Separator className="my-1" />

              <div className="grid grid-cols-2 gap-14">
                <div className="flex flex-col gap-1">
                  <p className="text-secondary-foreground text-sm">Total views</p>
                  <Skeleton className="h-5 w-full rounded-md" />
                </div>
                <div className="flex flex-col gap-1">
                  <p className="text-secondary-foreground text-sm">Avg time spend</p>
                  <Skeleton className="h-5 w-full rounded-md" />
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      <div className="mt-10 md:hidden">
        {!loading ? (
          dapps.filter((dapp) => active === dapp.category).length > 0 ? (
            <Swiper {...swiperConfig}>
              {dapps
                .filter((dapp) => active === dapp.category)
                .map((dapp) => {
                  return (
                    <SwiperSlide key={dapp.id}>
                      <DAppCard {...dapp} />
                    </SwiperSlide>
                  );
                })}
            </Swiper>
          ) : (
            <div className="flex flex-col items-center justify-center py-16 text-center">
              <div className="mb-4 text-6xl opacity-20">
                {active === 'DeFi' ? '💰' :
                 active === 'NFT' ? '🖼️' :
                 active === 'Games' ? '🎮' :
                 active === 'Tools' ? '🔧' :
                 active === 'Social' ? '👥' :
                 active === 'Multi-chain' ? '🔗' : '📱'}
              </div>
              <h3 className="text-xl font-semibold mb-2">No {active} DApps Found</h3>
              <p className="text-secondary-foreground">
                We couldn't find any DApps in the {active} category yet.
              </p>
            </div>
          )
        ) : (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DApps;
